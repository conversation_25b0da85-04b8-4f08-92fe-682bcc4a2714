name: "Staging: Update, Build & Deploy"
run-name: Deploy ${{ github.repository }} ${{ github.ref_name }} to Staging by @${{ github.actor }}

on:
   push:
        branches: [ "staging" ]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  staging-build-deploy:
    uses: s-Cube-Enterprise/reusable-workflows/.github/workflows/jib-build-publish.yaml@main
    with:
      AWS_REGION: us-east-1
      ECR_REPOSITORY: service_document_template
      EKS_DEPLOYMENT: scube-document-template-service-depl
      EKS_CONTAINER: scube-document-template-service
      KUBE_NAMESPACE: backend
      IMAGE_TAG: staging
      JAVA_VERSION: 21
      TARGET_ENVIRONMENT: STAGING
    secrets: inherit

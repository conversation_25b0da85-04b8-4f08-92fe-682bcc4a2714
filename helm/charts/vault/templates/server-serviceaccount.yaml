{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{ template "vault.serverServiceAccountEnabled" . }}
{{- if .serverServiceAccountEnabled -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "vault.serviceAccount.name" . }}
  namespace: hashicorp-vault
  labels:
    helm.sh/chart: {{ include "vault.chart" . }}
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if  .Values.server.serviceAccount.extraLabels -}}
      {{- toYaml .Values.server.serviceAccount.extraLabels | nindent 4 -}}
    {{- end -}}
  {{ template "vault.serviceAccount.annotations" . }}
{{ end }}

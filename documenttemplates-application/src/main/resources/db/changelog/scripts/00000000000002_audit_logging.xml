<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1694649913220-5">
        <renameColumn tableName="template" oldColumnName="documentuuid" newColumnName="document_uuid"
                      columnDataType="uuid"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694649913220-6">
        <renameColumn tableName="template" oldColumnName="subcategory" newColumnName="sub_category"
                      columnDataType="varchar(255 BYTE)"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694649913220-1">
        <dropDefaultValue columnDataType="varchar(250)" columnName="created_by" tableName="template"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694649913220-2">
        <dropDefaultValue columnDataType="timestamp" columnName="created_date" tableName="template"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694649913220-3">
        <dropDefaultValue columnDataType="varchar(250)" columnName="last_modified_by" tableName="template"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694649913220-4">
        <dropDefaultValue columnDataType="timestamp" columnName="last_modified_date" tableName="template"/>
    </changeSet>
</databaseChangeLog>

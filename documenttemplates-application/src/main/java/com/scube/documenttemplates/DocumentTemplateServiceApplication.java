package com.scube.documenttemplates;

import com.scube.audit.EnableAuditingLibrary;
import com.scube.rabbit.core.annotation.EnableRabbitMQLibrary;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@SpringBootApplication
@EnableRabbitMQLibrary(additionalPackages = "com.scube")
@EnableAuditingLibrary
@EnableJpaRepositories(basePackages = {"com.scube"})
@EntityScan(basePackages = {"com.scube"})
@OpenAPIDefinition(info = @Info(title = "Document Templates Service API", version = "1.0", description = "Swagger Documentation"))
public class DocumentTemplateServiceApplication {

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
                .setConnectTimeout(Duration.ofHours(1))
                .setReadTimeout(Duration.ofHours(1))
                .build();
    }

    public static void main(String[] args) {
        SpringApplication.run(DocumentTemplateServiceApplication.class, args);
    }
}
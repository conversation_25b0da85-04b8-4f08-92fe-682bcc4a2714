package com.scube.documenttemplates.service.helper;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

@Data
@Configuration
@ConfigurationProperties(prefix = "document-template-helper")
public class DocumentTemplateHelperProperties {
    private String url;
    private DataSize maxInMemorySize = DataSize.ofMegabytes(10);
}

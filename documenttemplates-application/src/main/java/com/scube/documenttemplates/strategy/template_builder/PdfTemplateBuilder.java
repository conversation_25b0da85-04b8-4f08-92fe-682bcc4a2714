package com.scube.documenttemplates.strategy.template_builder;

import com.scube.documenttemplates.dto.FillFileTemplateRequest;
import com.scube.documenttemplates.model.Template;
import com.scube.documenttemplates.rabbit.GenerateDocumentCommandHandler;
import com.scube.documenttemplates.service.DocumentTemplateService;
import com.scube.documenttemplates.service.FileService;
import com.scube.documenttemplates.service.TemplatingService;
import com.scube.documenttemplates.service.file_converter.IFileConverter;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;

@Slf4j
@Service("pdfTemplateBuilder")
@RequiredArgsConstructor
public class PdfTemplateBuilder implements ITemplateBuilder {
    private final FileService fileService;
    private final TemplatingService templatingService;
    private final DocumentTemplateService documentTemplateService;
    private final IFileConverter fileConverter;
    private final AmqpGateway amqpGateway;

    @Override
    public UUID fillFileTemplate(String data, String nameKey) {
        Template template = documentTemplateService.getTemplate(nameKey);

        byte[] templateFileData = fillPdf(data, template);

        var contentType = fileService.determineContentType("pdf");
        var fileResponse = fileService.saveFilledTemplateFile(templateFileData, template.getNameKey() + ".pdf", contentType);
        return fileResponse.getDocumentUUID();
    }

    @SneakyThrows
    public UUID fillFileTemplate(String data, MultipartFile template) {
        byte[] templateFileData = processWordTemplateAndConvertToPDF(data, template.getBytes());

        var contentType = fileService.determineContentType("pdf");
        var fileResponse = fileService.saveFilledTemplateFile(templateFileData, template.getName() + ".pdf", contentType);
        return fileResponse.getDocumentUUID();
    }

    @Override
    public void fillFileTemplateAsync(FillFileTemplateRequest request) {
        var startTime = System.currentTimeMillis();
        Template template = documentTemplateService.getTemplate(request.getNameKey());
        byte[] templateFileBytes = fileService.getTemplateFile(template);

        if (ObjectUtils.isEmpty(templateFileBytes)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Template file is empty");
        }

        var springContext = SecurityContextHolder.getContext();
        templatingService.processWordTemplateAsync(request.getDataAsString(), templateFileBytes)
                .flatMap(docx -> {
                    SecurityContextHolder.setContext(springContext);
                    if (ObjectUtils.isEmpty(docx))
                        throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Unable to process the template with data provided");

                    return fileConverter.convertDocxToPdfAsync(docx, request.getParentType(), request.getParentId());
                })
                .subscribe(x -> log.info("Generated docx in {} ms", System.currentTimeMillis() - startTime),
                        error -> {
                            SecurityContextHolder.setContext(springContext);
                            log.error("Error generating document: ", error);
                            amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedErrorEvent(
                                    request.getParentId(), request.getParentType(), error.getMessage())
                            );
                        });
    }

    public byte[] fillPdf(String data, Template template) {
        byte[] fileBytes = fileService.getTemplateFile(template);

        if (ObjectUtils.isEmpty(fileBytes)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Template file is empty");
        }

        return processWordTemplateAndConvertToPDF(data, fileBytes);
    }

    public byte[] processWordTemplateAndConvertToPDF(String data, byte[] fileBytes) {
        var startTime = System.currentTimeMillis();
        byte[] docx = templatingService.processWordTemplate(data, fileBytes);
        log.info("Generated docx in {} ms", System.currentTimeMillis() - startTime);

        if (ObjectUtils.isEmpty(docx))
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Unable to process the template with data provided");

        startTime = System.currentTimeMillis();
        var pdf = fileConverter.convertDocxToPdf(docx);
        log.info("Converted docx to pdf in {} ms", System.currentTimeMillis() - startTime);
        return pdf;
    }

    @Transactional
    public ResponseEntity<Resource> testFillTemplate(String data, String nameKey) {
        Template template = documentTemplateService.getTemplate(nameKey);

        byte[] pdf = fillPdf(data, template);

        var contentType = fileService.determineContentType("pdf");

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + template.getNameKey() + ".pdf" + "\"")
                .body(new ByteArrayResource(pdf));
    }
}

package com.scube.documenttemplates.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.documenttemplates.permission.Permissions;
import com.scube.documenttemplates.rabbit.GenerateDocumentCommandHandler;
import com.scube.documenttemplates.service.FileService;
import com.scube.lib.misc.annotations.validation.NoValidation;
import com.scube.rabbit.core.AmqpGateway;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/webhook/gotenberg")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.DOCUMENT_TEMPLATE_SERVICE)
@Validated
public class GotenbergWebhookController {
    private final FileService fileService;
    private final AmqpGateway amqpGateway;

    @PostMapping(consumes = MediaType.APPLICATION_PDF_VALUE)
    @RolesAllowed(Permissions.GotenbergWebhook.HANDLE_WEBHOOK)
    public ResponseEntity<String> handleWebhook(@RequestBody byte[] pdfBytes,
                                                @RequestHeader("Parent-Type") @Size(max = 255) String parentType,
                                                @RequestHeader("Parent-Id") @Size(max = 255) String parentId) {
        try {
            var contentType = fileService.determineContentType("pdf");
            var fileResponse = fileService.saveFilledTemplateFile(pdfBytes, UUID.randomUUID() + ".pdf", contentType);
            var documentId = fileResponse.getDocumentUUID();
            amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedEvent(parentId, parentType, documentId.toString()));
            return ResponseEntity.ok("Webhook received");
        } catch (Exception e) {
            amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedErrorEvent(parentId, parentType, e.getMessage()));
            throw e;
        }
    }

    @PostMapping("/error")
    @RolesAllowed(Permissions.GotenbergWebhook.HANDLE_WEBHOOK_ERROR)
    public ResponseEntity<String> handleWebhookError(@RequestBody @NoValidation String payload,
                                                     @RequestHeader("Parent-Type") @Size(max = 255) String parentType,
                                                     @RequestHeader("Parent-Id") @Size(max = 255) String parentId) {
        amqpGateway.publish(new GenerateDocumentCommandHandler.DocumentGeneratedErrorEvent(parentId, parentType, payload));
        return ResponseEntity.ok("Webhook received");
    }
}
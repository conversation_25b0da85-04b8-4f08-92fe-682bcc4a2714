package com.scube.documenttemplates.repository;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.documenttemplates.model.Template;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.Optional;

public interface TemplateRepository extends AuditableEntityRepository<Template, Long> {
    List<Template> findByCategory(@Size(max = 255) String category);

    Optional<Template> findByNameKey(@Size(max = 255) String nameKey);

    boolean existsByNameKey(@Size(max = 255) String nameKey);
}

<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                                       http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="create-refunded-detail-and-audit-log-table" author="karthik">
        <!-- Create refunded_detail table -->
        <createTable tableName="refunded_detail">
            <column name="refunded_detail_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_refunded_detail_id"/>
            </column>
            <column name="refund_transaction_id" type="BIGINT"/>
            <column name="refunded_parent_table" type="VARCHAR(50)"/>
            <column name="refunded_parent_id" type="UUID"/>
            <column name="refunded_amount" type="NUMERIC(38, 2)"/>
            <column name="refunded_detail_uuid" type="UUID">
                <constraints nullable="false" unique="true" uniqueConstraintName="uk_refunded_detail_uuid"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
        </createTable>

        <!-- Foreign key -->
        <addForeignKeyConstraint
                baseTableName="refunded_detail"
                baseColumnNames="refund_transaction_id"
                referencedTableName="refund_transaction"
                referencedColumnNames="refund_transaction_id"
                constraintName="fk_refunded_detail_refund_transaction"/>

        <!-- Create audit_log_refunded_detail table -->
        <createTable tableName="audit_log_refunded_detail">
            <column name="refunded_detail_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="pk_audit_refunded_detail_id"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_refunded_detail_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="refunded_detail_uuid" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="refund_transaction_id" type="BIGINT"/>
            <column name="refunded_parent_table" type="VARCHAR(50)"/>
            <column name="refunded_parent_id" type="UUID"/>
            <column name="refunded_amount" type="NUMERIC(38, 2)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user"/>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()"/>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()"/>
            <column name="properties" type="JSONB"/>
        </createTable>

    </changeSet>

</databaseChangeLog>

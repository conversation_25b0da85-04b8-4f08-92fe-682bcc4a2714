package com.scube.payment.features.providers.authorizedotnet.repo;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.payment.features.providers.authorizedotnet.model.AuthDotNetMapping;
import jakarta.validation.constraints.Size;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface AuthDotNetMappingRepository extends AuditableEntityRepository<AuthDotNetMapping, Long> {
    Optional<AuthDotNetMapping> findByRefId(@Size(max = 255) String refId);
    Optional<AuthDotNetMapping> findByTransactionId(@Size(max = 255) String transactionId);

    boolean existsByTransactionId(@Size(max = 255) String transactionId);

    @Query(value = "select payment.generate_ref_id()", nativeQuery = true)
    String getNewRefId();

    default AuthDotNetMapping findByTransactionIdOrThrow(@Size(max = 255) String transactionId) {
        return findByTransactionId(transactionId).orElseThrow();
    }

    default AuthDotNetMapping findByRefIdOrThrow(@Size(max = 255) String refId) {
        return findByRefId(refId).orElseThrow();
    }
}

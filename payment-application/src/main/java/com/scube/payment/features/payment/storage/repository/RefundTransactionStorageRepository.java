package com.scube.payment.features.payment.storage.repository;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.payment.features.payment.enums.RefundStatus;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import jakarta.validation.constraints.Size;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.time.Instant;

public interface RefundTransactionStorageRepository extends AuditableEntityRepository<RefundTransaction, Long> {

    Optional<RefundTransaction> findByRefundReference(@Size(max = 255) String refundReference);

    @Query("""
            SELECT rt FROM RefundTransaction rt
            WHERE (:statuses IS NULL OR rt.status IN :statuses)
            AND ( COALESCE(:startDate, '') = '' OR DATE(rt.createdDate) >= :startDate)
            AND ( COALESCE(:endDate, '') = '' OR DATE(rt.createdDate) <= :endDate)
            """)
    Page<RefundTransaction> findFilteredPaginatedRefundTransactions(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("statuses") List<RefundStatus> statuses,
            Pageable pageable
    );

    List<RefundTransaction> findByOrderId(UUID orderId);
}

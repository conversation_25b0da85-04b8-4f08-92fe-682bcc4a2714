package com.scube.payment.features.providers.authorizedotnet.dto.hosted_payment;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HostedPaymentSettings {

    private List<Setting> setting;

    @JsonPropertyOrder({"settingName", "settingValue"})
    @Data
    @AllArgsConstructor
    public static class Setting {
        private String settingName;
        private String settingValue;
    }
}

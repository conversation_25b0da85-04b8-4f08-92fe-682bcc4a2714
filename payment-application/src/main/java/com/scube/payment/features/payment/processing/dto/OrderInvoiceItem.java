package com.scube.payment.features.payment.processing.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scube.payment.features.payment.processing.serialization.BigDecimalTwoDecimalPlacesDeserializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(
        ignoreUnknown = true
)
public class OrderInvoiceItem {
    private Long orderItemId;

    private UUID itemId;

    private String itemType;

    private String primaryDisplay;

    private String secondaryDisplay;

    private Map<String, Object> properties;

    private List<OrderInvoiceItemFee> fees;

    @JsonDeserialize(using = BigDecimalTwoDecimalPlacesDeserializer.class)
    private BigDecimal total;

    @JsonDeserialize(using = BigDecimalTwoDecimalPlacesDeserializer.class)
    private BigDecimal subtotal;

    private List<OrderInvoiceItemFee> discountedItems;

    @JsonDeserialize(using = BigDecimalTwoDecimalPlacesDeserializer.class)
    private BigDecimal discount;

    @JsonDeserialize(using = BigDecimalTwoDecimalPlacesDeserializer.class)
    private BigDecimal stateFees;

    @JsonDeserialize(using = BigDecimalTwoDecimalPlacesDeserializer.class)
    private BigDecimal cityFees;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(
            ignoreUnknown = true
    )
    public static class OrderInvoiceItemFee implements Serializable {
        private String feeCode;

        private String label;

        private BigDecimal amount;
    }
}

package com.scube.payment.features.permission;

import jakarta.servlet.http.HttpServletRequest;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Formatter;

@Slf4j
@UtilityClass
public class SignatureVerificationUtil {
    public static boolean verifySignature(HttpServletRequest request, String headerKey, String tenantSignature) {
        ContentCachingRequestWrapper requestWrapper = (ContentCachingRequestWrapper) request;
        var requestBody = new String(requestWrapper.getContentAsByteArray(), StandardCharsets.UTF_8);

        String signatureHeader = requestWrapper.getHeader(headerKey);

        if (signatureHeader == null) {
            log.warn("Missing signature header in the request.");
            return false;
        }

        String[] parts = signatureHeader.split("=");
        String receivedSignature = parts.length > 1 ? parts[1] : "";

        try {
            String calculatedSignature = calculateHMAC(requestBody, tenantSignature);
            return calculatedSignature.equalsIgnoreCase(receivedSignature);
        } catch (Exception e) {
            log.error("Error calculating HMAC: ", e);
            return false;
        }
    }

    public static String calculateHMAC(String data, String key) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), "HmacSHA512");
        Mac mac = Mac.getInstance("HmacSHA512");
        mac.init(secretKeySpec);
        byte[] hmacData = mac.doFinal(data.getBytes());
        return toHexString(hmacData);
    }

    public static String toHexString(byte[] bytes) {
        Formatter formatter = new Formatter();
        for (byte b : bytes) {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }
}

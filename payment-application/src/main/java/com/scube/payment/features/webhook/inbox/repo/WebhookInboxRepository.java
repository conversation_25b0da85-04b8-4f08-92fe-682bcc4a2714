package com.scube.payment.features.webhook.inbox.repo;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.payment.features.webhook.inbox.model.InboxStatus;
import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import jakarta.validation.constraints.Size;

import java.util.List;

public interface WebhookInboxRepository extends AuditableEntityRepository<WebhookInbox, Long> {
    List<WebhookInbox> findByStatus(InboxStatus status);

    List<WebhookInbox> findByPaymentProvider(@Size(max = 255) String paymentProvider);

    List<WebhookInbox> findByStatusAndPaymentProvider(InboxStatus status, @Size(max = 255) String paymentProvider);

    boolean existsByProviderWebhookIdAndPaymentProvider(@Size(max = 255) String providerWebhookId, @Size(max = 255) String paymentProvider);
}
package com.scube.payment.features.webhook.inbox.rabbit;

import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import com.scube.payment.features.webhook.inbox.service.WebhookInboxProcessingService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class WebhookInboxListener extends FanoutListener<WebhookInboxListener.WebhookInboxEvent> {
    private final WebhookInboxProcessingService webhookInboxProcessingService;

    @Override
    public void consume(WebhookInboxEvent event) {
        webhookInboxProcessingService.processWebhook(event.webhookInbox());
    }

    public record WebhookInboxEvent(WebhookInbox webhookInbox) implements IRabbitFanoutSubscriber {}
}


package com.scube.payment.features.payment.validator;

import com.scube.payment.features.payment.processing.dto.IOrderPayment;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.BigDecimal;
import java.util.List;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidPaymentAmount.ValidPaymentAmountValidator.class)
public @interface ValidPaymentAmount {
    String message() default "Invalid Payment Amount.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Component
    @RequiredArgsConstructor
    class ValidPaymentAmountValidator implements ConstraintValidator<ValidPaymentAmount, IOrderPayment> {
        private final PaymentStorageService paymentStorageService;

        @Override
        public void initialize(ValidPaymentAmount constraintAnnotation) {
            ConstraintValidator.super.initialize(constraintAnnotation);
        }

        @Override
        public boolean isValid(IOrderPayment payment, ConstraintValidatorContext constraintValidatorContext) {
            if (paymentExceedsBalance(payment, constraintValidatorContext)) return false;

            return true;
        }

        private boolean paymentExceedsBalance(IOrderPayment payment, ConstraintValidatorContext constraintValidatorContext) {
            List<Payment> existingPayments = paymentStorageService.getPayments(payment.getOrderId());

            BigDecimal totalPaid = existingPayments.stream().map(Payment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal balance = payment.getOrderAmount().subtract(totalPaid);

            if (balance.compareTo(payment.getPaymentAmount()) < 0) {
                constraintValidatorContext.disableDefaultConstraintViolation();

                constraintValidatorContext
                        .buildConstraintViolationWithTemplate("Payment amount exceeds the outstanding balance.")
                        .addConstraintViolation();

                return true;
            }
            return false;
        }
    }
}

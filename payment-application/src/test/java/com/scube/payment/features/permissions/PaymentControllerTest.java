package com.scube.payment.features.permissions;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.scube.payment.SharedTestConfig;
import com.scube.payment.features.permission.Permissions;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

class PaymentControllerTest extends SharedTestConfig {
    @Test
    @WithJwt(json = MockMvcHelper.START_JSON + Permissions.Payment.GET_ALL_PAYMENTS + MockMvcHelper.END_JSON)
    void testGetAllPayments_Success() throws Exception {
        var result = MockMvcHelper.performGet(mockMvc, "/payments");
        assertNotEquals(403, result.getResponse().getStatus());
    }

    @Test
    void testGetAllPayments_Failure() throws Exception {
        var result = MockMvcHelper.performGet(mockMvc, "/payments");
        assertEquals(403, result.getResponse().getStatus());
    }
}
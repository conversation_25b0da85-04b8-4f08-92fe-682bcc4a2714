package com.scube.ai.observability;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Interceptor;
import org.hibernate.type.Type;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Map;

//https://www.baeldung.com/hibernate-interceptor
//https://stackoverflow.com/a/75136574/11703800
@Slf4j
@NoArgsConstructor
@Component
public class HibernateRequestResponseInterceptor implements Interceptor, Serializable, HibernatePropertiesCustomizer {

    @Override
    public boolean onLoad(Object entity, Object id, Object[] state, String[] propertyNames, Type[] types) {
        log.info("onLoad");
        return false;
    }

    @Override
    public boolean onFlushDirty(Object entity, Object id, Object[] currentState, Object[] previousState, String[] propertyNames, Type[] types) {
        log.info("onFlushDirty");
        return false;
    }

    @Override
    public boolean onSave(Object entity, Object id, Object[] state, String[] propertyNames, Type[] types) {
        log.info("onSave");
        return false;
    }

    @Override
    public void onDelete(Object entity, Object id, Object[] state, String[] propertyNames, Type[] types) {
        log.info("onDelete");
    }

    @Override
    public int[] findDirty(Object entity, Object id, Object[] currentState, Object[] previousState, String[] propertyNames, Type[] types) {
        log.info("findDirty");
        return null;
    }

    @Override
    public Object getEntity(String entityName, Object id) {
        log.info("getEntity");
        return null;
    }

    @Override
    public void customize(Map<String, Object> hibernateProperties) {
        hibernateProperties.put("hibernate.session_factory.interceptor", this);
    }
}


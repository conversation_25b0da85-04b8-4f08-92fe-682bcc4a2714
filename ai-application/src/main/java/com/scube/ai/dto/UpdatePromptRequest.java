package com.scube.ai.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdatePromptRequest {
    @NotNull
    private UUID uuid;
    @NotEmpty
    private String name;
    @NotEmpty
    private String description;
    @NotEmpty
    private String promptKey;
    @NotEmpty
    private String promptText;
}

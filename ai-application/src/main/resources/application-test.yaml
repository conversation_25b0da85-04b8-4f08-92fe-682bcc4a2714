spring:
  application:
    name: AIService
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 200MB 

  liquibase:
    enabled: true
    change-log: classpath:/db/changelog/db.changelog-master.xml

  jpa:
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false
    properties:
      hibernate:
        format_sql: true

open-ai:
  api-token: "testToken"
  completions-endpoint: "https://api.openai.com/v1/chat/completions"
  model: "gpt-3.5-turbo-0301"



keycloak:
  authoritiesConverterType: NATIVE
  host: http://localhost:8443/realms/master
  public-host: http://localhost:8443/realms/master
  admin:
    url: http://localhost:8443
    realm: master
    client-id: test
    client-secret: test
  swagger:
    url: http://localhost:8443
    realm: clerkXpress
    client-id: test

com.c4-soft.springaddons.oidc:
  ops:
    - iss: http://localhost:8443/realms/master
      jwkSetUri: http://localhost:8443/realms/master/protocol/openid-connect/certs
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - http://localhost:3000
          - https://localhost:3000
          - http://localhost:3030

multi-tenancy:
  enabled: false
  keycloak:
    enabled: false
  database:
    enabled: false

com.scube.client:
  auth: "http://${CLIENT_LIB_HOST:localhost}:9001/api/auth"

ai:
  lib:
    llm:
      enabled: true
      service: BedrockLLMService
      visionService: BedrockLLMWithVisionService
      openai:
        model: gpt-4o
        temperature: 0
        top-p: 1
        max-tokens: 4000
        baseUrl: https://api.openai.com
        apiKey: ${OPENAI_API_KEY}
      aws:
        bedrock:
          model: uus.anthropic.claude-3-7-sonnet-20250219-v1:0
          temperature: 0
          top-p: 1
          max-tokens: 2048
          timeout: 600 #seconds

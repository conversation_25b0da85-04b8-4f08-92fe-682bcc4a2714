# Development configurations
server:
  port: 9011
  servlet:
    context-path: /api/ai
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: always
    include-exception: true
    include-client-error-message: true
    include-server-error-message: true
    send-client-error-email: false
    send-server-error-email: false
  forward-headers-strategy: framework


logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    org.springframework.web: "info"
    org.hibernate: "error"
    liquibase: "info"

spring:
  application:
    name: AIService
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 200MB

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    show-sql: false



springdoc:
  show-actuator: true
  swagger-ui:
    filter: true


keycloak:
  host: http://keycloak.keycloak.svc.cluster.local:8080
  public-host: https://auth-dev.clerkxpress.com
  admin:
    url: ${keycloak.host}
    realm: master
    client-id: ${KEYCLOAK_ADMIN_CLIENT_ID}
    client-secret: ${KEYCLOAK_ADMIN_CLIENT_SECRET}
  swagger:
    url: ${keycloak.public-host}
    realm: clerkXpress
    client-id: ${KEYCLOAK_SWAGGER_AUTH_CLIENT_ID}

com.c4-soft.springaddons.oidc:
  ops:
    - iss: ${keycloak.host}
      jwkSetUri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
    - iss: ${keycloak.public-host}
      jwk-set-uri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - https://dev.clerkxpress.com
          - https://swagger-dev.clerkxpress.com
          - http://localhost:3000
          - https://localhost:3000

multi-tenancy:
  enabled: true
  async:
    enabled: true
  caching:
    enabled: true
  keycloak:
    enabled: true
  management:
    enabled: true
  rabbitmq:
    enabled: true
    host: "rabbitmq.backend.svc.cluster.local"
    port: 5672
  scheduling:
    enabled: true
  database:
    enabled: true
    type: DATABASE_PER_TENANT
    default-tenant: postgres
    tenancy-format: "%s"
    liquibase:
      change-log: "classpath:/db/changelog/db.changelog-master.xml"
    datasource:
      url: "**************************************************************"
      username: ${SPRING_DATASOURCE_USERNAME}
      password: ${SPRING_DATASOURCE_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximumPoolSize: 5
        minimumIdle: 5
        idleTimeout: 30000
        maxLifetime: 60000
        connectionTimeout: 30000

open-ai:
  api-token: ${OPEN_AI_API_TOKEN}
  completions-endpoint: "https://api.openai.com/v1/chat/completions"
  model: "gpt-3.5-turbo-0301"

scheduling:
  enabled: true

com.scube.client:
  auth: "http://scube-auth-service-srv:9001/api/auth"

ai:
  lib:
    llm:
      enabled: true
      service: BedrockLLMService
      visionService: BedrockLLMWithVisionService
      openai:
        model: gpt-4o
        temperature: 0
        top-p: 1
        max-tokens: 4000
        baseUrl: https://api.openai.com
        apiKey: ${OPENAI_API_KEY}
      aws:
        bedrock:
          model: us.anthropic.claude-3-7-sonnet-20250219-v1:0
          temperature: 0
          top-p: 1
          max-tokens: 2048
          timeout: 600 #seconds


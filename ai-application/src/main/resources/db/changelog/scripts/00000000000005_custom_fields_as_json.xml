<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="postgres (generated)" id="1711479888881-34">
        <addColumn tableName="audit_log_prompt">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-35">
        <addColumn tableName="prompt">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-36">
        <addColumn tableName="audit_log_prompt">
            <column defaultValueComputed="gen_random_uuid()" name="prompt_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-37">
        <addColumn tableName="prompt">
            <column defaultValueComputed="gen_random_uuid()" name="prompt_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-38">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_scheduler" constraintName="fkggi0jubkjahhqj63qb2hyn8kb" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-39">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_property_type" constraintName="fkph3vvdjiaedcco923ivk6qkxc" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-40">
        <dropUniqueConstraint constraintName="audit_log_property_type_property_type_uuid_key" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-41">
        <dropColumn columnName="created_by" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-42">
        <dropColumn columnName="created_date" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-1">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-2">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-3">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-4">
        <addNotNullConstraint columnDataType="timestamp" columnName="created_date" tableName="scheduler" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-5">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-6">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="last_modified_by" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-7">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-8">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-9">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-10">
        <dropNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-11">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-12">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-13">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-14">
        <addNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="scheduler" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-15">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-16">
        <dropNotNullConstraint columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-17">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-18">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-19">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-20">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-21">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-22">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-23">
        <dropNotNullConstraint columnDataType="uuid" columnName="property_type_uuid" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-24">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-25">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-26">
        <modifyDataType columnName="revision_id" newDataType="int" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-27">
        <dropNotNullConstraint columnDataType="smallint(5)" columnName="revision_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-28">
        <dropNotNullConstraint columnDataType="uuid" columnName="scheduler_uuid" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-29">
        <dropNotNullConstraint columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-30">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-31">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-32">
        <addUniqueConstraint columnNames="prompt_uuid" constraintName="uk_fq1sr5b6dtgspciomuu1tf7q8" tableName="prompt"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711479888881-33">
        <addUniqueConstraint columnNames="table_name, property_name" constraintName="property_type_unique" tableName="property_type"/>
    </changeSet>
</databaseChangeLog>

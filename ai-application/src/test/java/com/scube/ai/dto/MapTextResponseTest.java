package com.scube.ai.dto;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import static com.scube.ai.service.MapTextService.mergeSchemaWithResult;
import static org.junit.jupiter.api.Assertions.assertEquals;


public class MapTextResponseTest {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    @Test
    void mapTextResponseTestOfTest() throws JsonProcessingException {
        String requestSchema = """
        { 
            "name": "Bob",
            "sex": "Male"}
        """;

        JsonNode expected = objectMapper.readTree(requestSchema);

        String mappedText = """
        { 
            "name": "Bob",
            "test": "Should be Skipped"}
        """;

        JsonNode mappedTextJson = objectMapper.readTree(mappedText);

        MapTextResponse mapTextResponse = new MapTextResponse(mergeSchemaWithResult(expected, mappedTextJson));

        assertEquals(expected, mapTextResponse.getMappedText());
    }
}

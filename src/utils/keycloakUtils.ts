import Keycloak, { KeycloakInitOptions, KeycloakTokenParsed } from "keycloak-js";
import { useKeycloak } from "@/hooks/useKeycloak";
import { getKeycloakConfig } from "./authConfig";

const { url, clientId } = getKeycloakConfig();
const realm = process.env.NEXT_PUBLIC_CLERKXPRESS_REALM as string;

// Store Keycloak instances per realm. Undefined if not yet created.
const keycloakInstances: Record<string, Keycloak | undefined> = {};

// Flags and promises to control initialization per realm
let isInitializing: Record<string, boolean | undefined> = {};
let initializationPromises: Record<string, Promise<void> | undefined> = {};

// Function to initialize the Keycloak instance only once per realm
const initializeKeycloakInstance = () => {
  if (!keycloakInstances[realm]) {
    keycloakInstances[realm] = new Keycloak({
      url,
      realm,
      clientId,
    });
  } else {
    console.log(`Keycloak instance already initialized for realm: ${realm}`);
  }
};

export const initKeycloak = async (
  onAuthenticatedCallback: () => void,
): Promise<void> => {
  // If there is already an initialization promise for this realm, return it
  if (initializationPromises[realm] !== undefined) {
    return initializationPromises[realm]!;
  }

  const initPromise = new Promise<void>(async (resolve, reject) => {
    try {
      // If the instance exists and is already authenticated, simply call the callback and resolve
      if (keycloakInstances[realm] && keycloakInstances[realm]!.authenticated) {
        console.log(`Keycloak instance for realm "${realm}" is already initialized and authenticated.`);
        onAuthenticatedCallback();
        return resolve();
      }

      isInitializing[realm] = true;
      initializeKeycloakInstance();
      const instance = keycloakInstances[realm];
      if (!instance) {
        console.error(`Keycloak instance not initialized for realm: ${realm}`);
        return reject(new Error("Keycloak instance not created"));
      }

      const initOptions: KeycloakInitOptions = {
        onLoad: "check-sso",
        pkceMethod: "S256",
        silentCheckSsoRedirectUri: origin + "/silent-check-sso.html",
      };

      const authenticated = await instance.init(initOptions);

      if (authenticated) {
        console.log(`User is authenticated in realm: ${realm}`);
      } else {
        console.log(`User is not authenticated in realm: ${realm}`);
      }

      onAuthenticatedCallback();
      resolve();
    } catch (error) {
      reject(error);
    } finally {
      isInitializing[realm] = false;
    }
  });

  initializationPromises[realm] = initPromise;
  return initPromise;
};

export const login = () => keycloakInstances[realm]?.login();
export const register = () => keycloakInstances[realm]?.register();
export const hintLogin = (hint: string) =>
  keycloakInstances[realm]?.login({ idpHint: hint });
export const logout = () => keycloakInstances[realm]?.logout();
export const getToken = () => keycloakInstances[realm]?.token;
export const getTokenParsed = () => keycloakInstances[realm]?.tokenParsed;
export const getUsername = () =>
  keycloakInstances[realm]?.tokenParsed?.preferred_username;
export const getUserProfile = () =>
  keycloakInstances[realm]?.loadUserInfo() || Promise.resolve(undefined);
export const updateToken = (successCallback: () => void) =>
  keycloakInstances[realm]
    ?.updateToken(5)
    .then(successCallback)
    .catch(login);
export const clearToken = () => keycloakInstances[realm]?.clearToken();
export const isLoggedIn = () =>
  Boolean(keycloakInstances[realm]?.authenticated);

export const refreshTokenIfNeeded = async (minValidity: number = 30 * 10) => {
  if (keycloakInstances[realm]) {
    try {
      const refreshed = await keycloakInstances[realm]?.updateToken(minValidity);
      if (refreshed) {
        console.debug("Token refreshed.");
      }
    } catch {
      console.log("Token refresh failed.");
      clearToken();
    }
  } else {
    console.error("Keycloak instance is not initialized.");
  }
};

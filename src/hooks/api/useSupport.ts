"use client";
import { getIssues } from "@/app/(app)/(realm)/(protected)/(app)/support/issues/actions/getIssues";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { user<PERSON>tom } from "@/components/sidebar/main/User";
import { getIssue } from "@/app/(app)/(realm)/(protected)/(app)/support/issues/actions/getIssue";
import { createIssue } from "@/app/(app)/(realm)/(protected)/(app)/support/issues/actions/createIssue";
import { addCommentToTicket } from "@/app/(app)/(realm)/(protected)/(app)/support/issues/actions/addCommentToTicket";
import { useMyProfile } from "../providers/useMyProfile";
import { requests } from "@/utils/agent";

export const useGetProjectIssues = (projectId: number | null) => {
  const [user] = useAtom(userAtom);
  const apiKey = user?.redmine_api_key ?? null;

  return useQuery({
    queryKey: ["projectIssues", projectId],
    queryFn: () => getIssues(projectId!, apiKey),
    enabled: !!projectId
  });
};

export const useGetProjectIssue = (ticketId: number) => {
  const [user] = useAtom(userAtom);
  const apiKey = user?.redmine_api_key ?? null;
  return useQuery({
    queryKey: ["projectIssue", ticketId],
    queryFn: () => getIssue(ticketId, apiKey),
    enabled: !!ticketId && !!apiKey
  });
};

export const useCreateProjectIssue = (projectId: number) => {
  const [user] = useAtom(userAtom);
  const { refetch } = useGetProjectIssues(projectId)
  const apiKey = user?.redmine_api_key ?? null;

  return useMutation((input: { projectId: number; data: any }) =>
    createIssue(input.projectId, input.data, apiKey),
    {
      onSuccess: () => {
        refetch();
      },
      onError: (error) => {
        console.log(error)
      },
    }
  );
};

export const useGetRedmineProjectId = () => {
  return useQuery({
    queryKey: ["redmineProjectId"],
    queryFn: () => {
      return requests.get<any>(`/config/app-property/redmineProjectId`);
    },
  });
};

export const useUpdateRedmineProjectId = () => {
  return useMutation({
    mutationFn: (projectId: number) => {
      return requests.put<any>(`/config/app-property`, { redmineProjectId: projectId });
    },
  });
};

export const useCreateCommentOnTicket = (ticketId: number) => {
  const [user] = useAtom(userAtom);
  const apiKey = user?.redmine_api_key ?? null;
  return useMutation((comment: string) =>
    addCommentToTicket(ticketId, comment, apiKey)
  );
};

interface TenantInformation {
  adminCity: string;
  clerkName: string;
  adminState: string;
  clerkEmail: string;
  clerkTitle: string;
  adminOffice: string;
  adminStreet: string;
  templateKey: string;
  adminZipCode: string;
  clerkSignature: string | null;
  clerkXpressUrl: string;
  adminOfficeRoom: string;
  clerkPhoneNumber: string;
  cityClerkOfficeName: string;
}


export const useGetTenantInformation = () => {
  const { hasPermissions } = useMyProfile();

  return useQuery({
    queryKey: ["tenantInformationConfig"],
    queryFn: () =>
      hasPermissions(["super-admin"])
        ? requests.get<TenantInformation>(`/config/json-storage?config=tenant`)
        : requests.get<TenantInformation>(`/config/me/json-storage?config=tenant`),
  });
};
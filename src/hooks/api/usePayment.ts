import { useQuery, useMutation } from "@tanstack/react-query";
import { requests } from "@/utils/agent";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const payment = "/calculation/payments";

type ReceiptData = {
  orderId: string;
  receipts: {
    paymentId: string;
    transactionDate: string;
    receiptUrl: string;
  }[];
};

// Process payment, send order and amount applied.
export const useProcessPayment = (
  orderId: string,
  amount: string,
  paymentType: string,
) => {
  return useMutation(() =>
    requests.post<any>(payment + `/${orderId}/create`, { amount, paymentType }),
  );
};

export const useStripeProcessPayment = (
  orderId: string,
  amount: string,
  paymentType: string,
) => {
  return useMutation(() =>
    requests.post<any>(payment + `/${orderId}/create`, { amount, paymentType }),
  );
}

export const useGetStripeKey = () => {
  // Only use on admin when ready for admin's to take payments through there system. -- Sean B
  return useQuery({
    queryKey: ["stripeKey"],
    queryFn: () =>  requests.get<any>(`/config/app-property/payment-providers.stripe.publishable-key`)
  });
}

// Cancel/Void Payment by payment ID
export const useCancelPayment = (paymentId: string) => {
  return useMutation(() =>
    requests.put<any>(payment + `/${paymentId}/cancel`, {}),
  );
};

// Get all voided payments by date
export const useGetAllVoidedPayments = (
  startDate: Date,
  endDate: Date,
  limit: number,
  page: number,
) => {
  return useQuery({
    queryKey: ["voidedPayments", startDate, endDate, limit, page],
    queryFn: () =>
      requests.get<any>(
        payment +
          `/voided?dateFrom=${startDate}&dateTo=${endDate}&limit=${limit}&page=${page}`,
      ),
  });
};

// Get Receipt
export const useGetReceipt = (orderId: string, options?: any) => {
  const { hasPermissions } = useMyProfile();
  const permitted = hasPermissions(["super-admin"]);

  return useQuery<ReceiptData>({
    queryKey: ["receipt", orderId],
    queryFn: () =>
      permitted
        ? requests.get(`/coordinator/payment/${orderId}/receipts`)
        : requests.get(`/coordinator/me/payment/${orderId}/receipts`),
    ...options,
    enabled: !!orderId,
  });
};

interface FeeEvent {
  uuid: string;
  eventTypeId: number;
  code: string;
  name: string;
  description: string;
  createdBy: string;
  createdDate: string;
  comment: string | null;
  action: string;
}

interface Association {
  entityType: string;
  entityId: string;
}

export interface Fee {
  entityId: string;
  entityType: string;
  events: FeeEvent[];
  feeCode: string;
  feeName: string;
  feeStatus: string;
  feeAmount: number;
  feePaidDate: string | null;
  orderId: string | null;
  comment: string;
  createdDateTime: string;
  updatedDateTime: string;
  createdBy: string;
  updatedBy: string;
  associations: Association[];
}

// Add Fee to Entity
export const useAddFee = () => {

  return useMutation(
    ({
      body,
    }: {
      body: {
        feeCode: string;
        amount: number;
        comment: string;
        associations: {
          entityId: string | null;
          entityType: string | null;
        }[];
      };
    }) =>
      requests.post<Fee>(
        `/license/entity-fee`,
        body,
      ),
  );
};
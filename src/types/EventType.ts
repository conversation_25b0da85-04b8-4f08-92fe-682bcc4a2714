export interface Event {
  // Displays the message of what has been changed. (Ex: "Name changed to <PERSON>")
  action: string | null;
  // The id of the event in reference to the primary key in the database.
  uuid: string;
  // this is the number of the event type in the database.
  eventTypeId: number;
  // The user who created the event.
  createdBy: string;
  // The date the event was created.
  createdDate: string;
  // Any comments inputed by the user associated with the event.
  comment: string | null;
  // The key for the type of event
  code: string;
  // Description of the event Ex: "Name Changed"
  description: string;
  // The name of the event
  name: string;
}

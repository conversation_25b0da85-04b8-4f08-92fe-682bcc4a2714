import { requests } from "../utils/agent";

const profiles = {
  getHeader: (entitytype: string, entityId: string) =>
    requests.get<any>(
      `/license/profile/header?profileType=${entitytype}&entityId=${entityId}`
    ),
  getLicenses: (entitytype: string, entityId: string) =>
    requests.get<any>(
      `/license/profile/license?profileType=${entitytype}&entityId=${entityId}`
    ),
  getContacts: (entitytype: string, entityId: string) =>
    requests.get<any>(
      `/license/profile/contact?profileType=${entitytype}&entityId=${entityId}`
    ),
  getAffiliations: (entitytype: string, entityId: string) =>
    requests.get<any>(
      `/license/profile/affiliation?profileType=${entitytype}&entityId=${entityId}`
    ),
};

const events = {
  getEntity: (entitytype: string, entityId: string) =>
    requests.get<any>(`/license/profile/${entitytype}/${entityId}`),
  dog: {
    setDangerousDogTrue: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/dogSetDangerousTrue`,
        body
      ),
    setDangerousDogFalse: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/dogSetDangerousFalse`,
        body
      ),
    setDogDeceased: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/dogDeceased`,
        body
      ),
    reactivateDog: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/dogReactivated`,
        body
      ),
    setDogLost: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/dogLost`,
        body
      ),
    setDogFound: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/dogFound`,
        body
      ),
    setDogRelinquished: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/dogRelinquished`,
        body
      ),
  },
  individual: {
    setIndividualDeceased: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/individualDeceased`,
        body
      ),
    setIndividualMovedOutsideJurisdiction: (
      entitytype: string,
      entityId: string,
      body: any
    ) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/individualMovedOutsideJurisdiction`,
        body
      ),
    reactivateIndividual: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/individualReactivated`,
        body
      ),
  },
  license: {
    setLicenseCanceled: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/licenseCanceled`,
        body
      ),
    setLicenseCorrected: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/licenseCorrected`,
        body
      ),
    setLicenseReactivated: (entitytype: string, entityId: string, body: any) =>
      requests.post<any>(
        `license/event/${entitytype}/${entityId}/licenseReactivated`,
        body
      ),
  },
};

const license = {
  profiles,
  events,
};

export default license;

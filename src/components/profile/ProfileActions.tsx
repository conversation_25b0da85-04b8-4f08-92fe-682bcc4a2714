import React, { useState } from "react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import {
  EventType,
  useGetDropdownEvents,
  useGetProfile,
} from "@/hooks/api/useProfiles";

import { addDays } from "date-fns";
import TransferLicense from "./events/dog/DogLicenseTransfer";
import {
  dogEventsList,
  individualEventsList,
} from "@/app/(app)/(realm)/(protected)/(app)/entity/[entitytype]/[entityId]/history/reusables";
import { useQueryClient } from "@tanstack/react-query";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { useEntity } from "@/hooks/providers/useEntity";

const eventsList: any = {
  individual: {
    ...individualEventsList,
  },
  dog: {
    ...dogEventsList,
  },
};

const pullData = (inputDate: any) => {
  const today = new Date();
  const givenDate = new Date(inputDate);

  const thirtyDaysBeforeExpiry = addDays(givenDate, -30);

  if (today < thirtyDaysBeforeExpiry) {
    return true;
  } else {
    return false;
  }
};

type ProfileActionsProps = {
  children: React.ReactNode;
  className?: string;
  entityType: string;
  entityId: string;
};

const selectStyle = (disabled: boolean) => {
  if (disabled) {
    return "disabled";
  }

  return "default";
};

export default function ProfileActions({
  entityType,
  entityId,
  children,
  className,
}: ProfileActionsProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { hasPermissions } = useMyProfile();
  const {
    data: entity,
    isLoading: isProfileLoading,
    isError: isProfileError,
  } = useGetProfile(entityType, entityId);

  const admin: boolean = hasPermissions(["super-admin"]);

  // Is Individual and is not admin

  const { data, isLoading, isError } = useGetDropdownEvents(admin);
  const [modalIsOpen, setModalIsOpen] = useState(false);

  // Function to toggle modal
  const toggleModal = () => {
    setModalIsOpen(!modalIsOpen);
  };

  const filteredEvents =
    data?.filter((event: EventType) => event.profileType === entityType) || [];

  const license = entity?.[0];

  const isLost = entity?.[entityType as string]?.status === "Lost";
  const isDangerous = entity?.[entityType as string]?.isDangerous;
  const isDeceased = entity?.[entityType as string]?.status === "Deceased";
  const isTransferred =
    entity?.[entityType as string]?.status === "Transferred";

  const checkStatus = (list: string[]) => {
    const hasStatus = list.some((item) => {
      switch (item) {
        case "Lost":
          return isLost;
        case "NotLost":
          return !isLost;
        case "Dangerous":
          return isDangerous;
        case "NotDangerous":
          return !isDangerous;
        case "Deceased":
          return isDeceased;
        case "Transferred":
          return isTransferred;
        default:
          return false;
      }
    });

    return hasStatus;
  };

  const routesMap: { [key: string]: string } = {
    dogTransferOfOwnership: `/entity/${entityType}/transferOfOwnership?entityType=${entityType}&entityId=${entityId}&fetchData=${pullData(
      license?.validToDate,
    )}`,
    individualAddressChange: `/entity/${entityType}/changeAddress?entityType=${entityType}&entityId=${entityId}`,
  };

  const linkModes: any = {
    modal: () => toggleModal(),
    link: (link: string) => router.push(routesMap[link]),
  };

  const styleVariants = {
    default: className,
    disabled:
      "flex items-center justify-center gap-2 text-black py-2 px-4 rounded-md bg-neutral-400 transition-all hover:bg-neutral-300 cursor-not-allowed shrink-0",
  };

  const triggerDisabled = !eventsList[entityType];
  const isMobile = window.innerWidth < 768;

  if (isLoading || isProfileLoading) return null;
  if (isError || isProfileError) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        disabled={triggerDisabled}
        className={styleVariants[selectStyle(triggerDisabled)]}
      >
        {children}
      </DropdownMenuTrigger>
      <DropdownMenuContent align={isMobile ? "center" : "end"}>
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {filteredEvents.map((event: EventType) => {
          console.log(event);

          const entityEventList = eventsList[entityType];

          const disabled = entityEventList[event.code]?.disabled;
          const Component = entityEventList[event.code]?.component;
          const link = entityEventList[event.code]?.link;

          // TODO: Fix this later. Transfer of ownership for residents needs to be a different link. Disabled for now. -- Sean B
          if (event.code === "dogTransferOfOwnership" && !admin) {
            return null;
          }

          if (!Component && !link) {
            return null;
          }

          if (!Component && link) {
            const type = entityEventList[event.code]?.link?.type;
            const mode = entityEventList[event.code]?.link?.value;

            console.log(disabled);

            return (
              <DropdownMenuItem
                className="w-full cursor-pointer text-left"
                key={event.code}
                disabled={disabled && checkStatus(disabled)}
                onClick={(e) => {
                  e.preventDefault();
                  linkModes[type](mode);
                  queryClient.invalidateQueries([
                    "profile",
                    entityType,
                    entityId,
                  ]);
                }}
              >
                {event.name}
              </DropdownMenuItem>
            );
          }

          return (
            <DropdownMenuItem
              key={event.code}
              disabled={disabled && checkStatus(disabled)}
            >
              <Component
                event={event}
                entityType={entityType}
                entityId={entityId}
              />
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
      <TransferLicense
        isOpen={modalIsOpen}
        onClose={toggleModal}
        entityId={entityId}
        entityType={entityType}
        fetchData={pullData(license?.validToDate) ?? false}
      />
    </DropdownMenu>
  );
}

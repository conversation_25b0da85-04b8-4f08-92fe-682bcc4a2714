import React from 'react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'

interface ToggleProps {
  state: boolean
  setState: (state: boolean) => void
  label: string
}

const Toggle: React.FC<ToggleProps> = ({ state, setState, label }) => {
  return (
    <div className="flex items-center space-x-2">
      <Switch 
        checked={state} 
        onCheckedChange={setState} 
        className="cursor-pointer" 
      />
      <Label className="text-sm font-medium text-gray-900 dark:text-gray-300">
        {label}
      </Label>
    </div>
  )
}

export default Toggle

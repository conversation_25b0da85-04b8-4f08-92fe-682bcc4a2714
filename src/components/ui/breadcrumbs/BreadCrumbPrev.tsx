"use client"
import { useEffect, useState } from 'react'
import Link from "next/link"
import { FiArrowLeft } from "react-icons/fi"
import { usePathname } from "next/navigation"

const usePreviousUrl = () => {
  const [prevUrl, setPrevUrl] = useState<string | null>("");
  const pathname = usePathname();

  useEffect(() => {
    setPrevUrl(pathname);
  }, [pathname]);

  return prevUrl;
};

const BreadCrumbPrev = ({
  label
}:{
  label: string
}) => {
  const prevUrl = usePreviousUrl();

  return (
    <div>
      <Link href={prevUrl || '/'}>
        <div className='
          flex items-center gap-1 italic text-sm
          hover:text-blue-500 hover:underline
        '>
          <FiArrowLeft />
          {`${label}`}
        </div>
      </Link>
      <h1 className='text-2xl font-bold mb-4'>Checkout</h1>
    </div>
  )
}

export default BreadCrumbPrev

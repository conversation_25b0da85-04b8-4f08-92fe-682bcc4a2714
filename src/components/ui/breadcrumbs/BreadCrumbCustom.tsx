"use client"

import Link from "next/link"
import { FiArrowLeft } from "react-icons/fi"

const BreadCrumbCustom = ({
  returnTo,
  label
}:{
  returnTo: any;
  label:string
}) => {

  return (
    <div>
      {returnTo && 
        <Link href={returnTo} 
          className='
            flex items-center gap-1 italic text-sm
            hover:text-blue-500 hover:underline
          '
        ><FiArrowLeft />
          {`${label}`}
        </Link>
      }
      <h1 className='text-2xl font-bold mb-4'>Checkout</h1>
    </div>
  )
}

export default BreadCrumbCustom
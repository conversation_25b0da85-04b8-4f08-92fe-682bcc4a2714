"use client";
import React, { useEffect } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { BiDownload } from "react-icons/bi";

import ReportComponent from "@/components/reports/ReportComponent";
import { useAtom } from "jotai";
import {
  ReportType,
  downloadListAtom,
  modalOpenAtom,
  updateDownloadList,
} from "@/components/reports/scripts/downloadCenterUtils";
import { requests } from "@/utils/agent";

const PendingNotification = () => {
  return (
    <div
      className="
        absolute -top-1 -right-1 bg-yellow-500 w-2 h-2 rounded
      "
    >
      <div className="animate-ping absolute top-0 right-0 inline-flex h-full w-full rounded-full bg-yellow-500 opacity-75"></div>
    </div>
  );
};

export default function DownloadContainer() {
  const [downloadList, setDownloadList] = useAtom(downloadListAtom);
  const [modalOpen, setModalOpen] = useAtom(modalOpenAtom);

  const pending = downloadList.filter(
    (download: ReportType) => download.status === "PENDING"
  );

  const updatePendingReports = async () => {
    if (pending.length === 0) {
      return;
    }

    for (const report of pending) {
      const currentReport: ReportType = await requests.get(
        `/report/report/${report.reportId}`
      );
      currentReport.title = report.title;

      if (currentReport.status !== "PENDING") {
        const updatedList = updateDownloadList(downloadList, currentReport);
        setDownloadList(updatedList);
        setModalOpen(true);
      }
    }
  };

  useEffect(() => {
    const list = localStorage.getItem("downloadList");
    if (list) {
      setDownloadList(JSON.parse(list));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      updatePendingReports();
    }, 2000);

    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pending]);

  return (
    <Popover
      open={modalOpen}
      onOpenChange={() => {
        setModalOpen(!modalOpen);
      }}
    >
      <PopoverTrigger className="relative">
        <BiDownload className="text-xl text-neutral-500" />
        {pending.length > 0 ? <PendingNotification /> : null}
      </PopoverTrigger>
      <PopoverContent className="mr-4 ">
        {downloadList.length > 0 ? (
          <div className="flex flex-col gap-3 max-h-96 overflow-y-auto w-full ">
            {downloadList.map((report: ReportType) => (
              <ReportComponent report={report} key={report.reportId} />
            ))}
          </div>
        ) : (
          <>
            <div className="text-neutral-800 font-semibold">
              Empty - No Downloads
            </div>
          </>
        )}
      </PopoverContent>
    </Popover>
  );
}

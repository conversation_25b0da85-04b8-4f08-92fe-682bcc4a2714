package com.scube.licensing.features.participant;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.scube.licensing.SharedTestConfig;
import com.scube.licensing.features.participant.exception.ParticipantNotFoundException;
import com.scube.licensing.features.participant.dto.CreateParticipantResponseDTO;
import com.scube.licensing.features.permission.Permissions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static com.scube.licensing.features.permissions.MockMvcHelper.END_JSON;
import static com.scube.licensing.features.permissions.MockMvcHelper.START_JSON;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class ParticipantControllerTest extends SharedTestConfig {
    @Test
    @Order(1)
    @WithJwt(json = START_JSON + Permissions.Participant.CREATE_RESIDENT + END_JSON)
    void createParticipant() throws Exception {
        CreateParticipantResponseDTO resDto = getTestUtils().createResidentAsClerk();
        assertNotNull(resDto);
        assertNotNull(resDto.getEntityId());
    }

    @Test
    @Order(2)
    @WithJwt(
            json = START_JSON +
                    Permissions.Participant.CREATE_RESIDENT + "," +
                    Permissions.Participant.DELETE_PARTICIPANT +
                    END_JSON
    )
    void deleteParticipant() throws Exception {
        CreateParticipantResponseDTO participant = getTestUtils().createResidentAsClerk();
        assertNotNull(participant);
        assertNotNull(participant.getEntityId());

        var entityId = participant.getEntityId();

        var res = mockMvc.perform(MockMvcRequestBuilders
                .delete("/participant/" + entityId)
        ).andExpect(status().isNoContent());

        assertThrows(ParticipantNotFoundException.class, () -> participantService.getParticipantOrElseThrow(entityId));
    }
}
CREATE OR REPLACE FUNCTION end_of_month_plus_one_year(input_date TIMESTAMPTZ)
RETURNS TIMESTAMPTZ AS $$
DECLARE
    end_of_month TIMESTAMPTZ;
BEGIN
    -- Calculate the end of the month
    end_of_month := (date_trunc('month', input_date) + INTERVAL '1 month - 1 second')::TIMESTAMPTZ;

    -- Add one year to the end of the month date
    RETURN end_of_month + INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql;
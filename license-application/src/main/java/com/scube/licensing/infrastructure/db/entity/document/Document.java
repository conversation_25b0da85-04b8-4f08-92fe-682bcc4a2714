package com.scube.licensing.infrastructure.db.entity.document;

import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import com.scube.licensing.infrastructure.db.entity.association.Deletable;
import jakarta.persistence.*;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(
        name = Document.TABLE_NAME,
        indexes = {
                @Index(name = "idx_document_document_uuid", columnList = Document.DOCUMENT_UUID),
                @Index(name = "idx_document_document_type_id", columnList = DocumentType.DOCUMENT_TYPE_ID)
        }
)
@Getter
@Setter
@Accessors(chain = true)
@Audited
public class Document extends Associable implements Deletable {
    public static final String TABLE_NAME = "document";
    public static final String ENTITY_TYPE = "document";
    public static final String DOCUMENT_ID = "document_id";
    public static final String DOCUMENT_UUID = "document_uuid";

    @Size(max = 255)
    private String contentType;

    @Size(max = 255)
    private String fileName;

    @PositiveOrZero
    private Long size;

    @Size(max = 255)
    private String url;

    @Column(nullable = false)
    private UUID documentServiceUuid;

    private Instant deletedDate;

    private boolean isDeleted;

    @JoinColumn(name = DocumentType.DOCUMENT_TYPE_ID, nullable = false)
    @ManyToOne
    private DocumentType documentType;

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    /*
     * Associations
     */
    // dummy column
    @Enumerated(EnumType.STRING)
    @Column(name = "dummy_column")
    private AssociationType associationType = AssociationType.DOCUMENT;


    @OneToMany(mappedBy = "parentDocument", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> childAssociations = new HashSet<>();

    @OneToMany(mappedBy = "childDocument", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> parentAssociations = new HashSet<>();

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }
}

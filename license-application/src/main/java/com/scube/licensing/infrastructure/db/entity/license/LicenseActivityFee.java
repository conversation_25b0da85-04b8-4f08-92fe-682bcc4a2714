package com.scube.licensing.infrastructure.db.entity.license;

import com.scube.licensing.features.events.Event;
import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.springframework.lang.NonNull;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Entity
@Table(
        name = LicenseActivityFee.TABLE_NAME,
        indexes = {
                @Index(name = "idx_license_activity_fee_activity_type", columnList = LicenseActivity.LICENSE_ACTIVITY_ID),
                @Index(name = "idx_license_activity_fee_fee_code", columnList = LicenseActivityFee.FEE_CODE),
                @Index(name = "idx_license_activity_fee_order_id", columnList = LicenseActivityFee.ORDER_ID)
        }
)
@Getter
@Setter
@Accessors(chain = true)
@Audited
public class LicenseActivityFee extends BaseEntity {
    public static final String TABLE_NAME = "license_activity_fee";
    public static final String LICENSE_ACTIVITY_FEE_ID = "license_activity_fee_id";
    public static final String L_AMOUNT = "amount";
    public static final String ORDER_ID = "order_id";
    public static final String FEE_CODE = "fee_code";

    @Size(max = 255)
    @Column(name = FEE_CODE)
    private String feeCode;

    @Column(name = L_AMOUNT)
    private BigDecimal amount;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PaymentStatus paymentStatus;

    @ManyToOne
    @JoinColumn(name = LicenseActivity.LICENSE_ACTIVITY_ID)
    private LicenseActivity licenseActivity;

    @Column(name = ORDER_ID)
    private UUID orderId;

    private Instant paidDate;

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public boolean isUnpaid() {
        return PaymentStatus.UNPAID.equals(paymentStatus);
    }

    public boolean isPaid() {
        return PaymentStatus.PAID.equals(paymentStatus);
    }

    public void setReason(String reason) {
        setProperty("reason", reason);
    }

    public void changeFeeAmount(@Min(0) @NonNull BigDecimal feeAmount, String reason) {
        setAmount(feeAmount);
        setReason(reason);
        logChangeEvent("License fee amount changed", feeAmount, reason);
    }

    public void logChangeEvent(String action, BigDecimal feeAmount, String reason) {
        License license = getLicenseActivity().getLicense();
        if (getUuid() == null) {
            setUuid(UUID.randomUUID());
        }
        license.addEvent(new Event(
                "licenseChanged",
                action,
                Map.of(
                        "licenseActivityFeeId", getUuid().toString(),
                        "feeCode", feeCode,
                        "amount", feeAmount.toString(),
                        "reason", reason
                )
        ));
    }

    @AllArgsConstructor
    @Getter
    public enum PaymentStatus {
        UNPAID("unpaid"),
        PAID("paid"),
        PARTIALLY_PAID("partially_paid");

        private final String key;
    }
}
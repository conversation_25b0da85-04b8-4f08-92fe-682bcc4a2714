package com.scube.licensing.infrastructure.db.repository.association;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface AssociationRepository extends AuditableEntityRepository<Association, Long> {
    //get by parentId and parentAssociationType and childAssociationType
    Optional<Association> findFirstByParentIdAndParentAssociationTypeAndChildAssociationType(Long parentId, AssociationType parentAssociationType, AssociationType childAssociationType);

    List<Association> findAllByParentIdAndParentAssociationTypeAndChildAssociationType(Long parentId, AssociationType parentAssociationType, AssociationType childAssociationType);

    Set<Association> findAllByParentIdAndParentAssociationType(Long parentId, AssociationType parentAssociationType);

    Set<Association> findAllByChildIdAndChildAssociationType(Long childId, AssociationType childAssociationType);

    //exists by parentId and parentAssociationType and childId and childAssociationType
    Optional<Association> findByParentIdAndParentAssociationTypeAndChildIdAndChildAssociationType(Long parentId, AssociationType parentAssociationType, Long childId, AssociationType childAssociationType);

    void deleteAllByParentIdAndParentAssociationType(Long parentId, AssociationType parentAssociationType);

    void deleteAllByChildIdAndChildAssociationType(Long childId, AssociationType childAssociationType);
}
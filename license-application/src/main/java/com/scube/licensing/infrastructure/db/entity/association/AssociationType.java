package com.scube.licensing.infrastructure.db.entity.association;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.web.server.ResponseStatusException;

@Getter
public enum AssociationType {
    PARTICIPANT("participant"),
    ADDRESS("address"),
    CUSTOM_ENTITY("customEntity"),
    LICENSE("license"),
    DOCUMENT("document"),
    ENTITY_FEE("entityFee"),
    BUSINESS("business"),
    ENTITY_NOTE("entityNote"),
    ENTITY_GROUP("entityGroup");

    private final String key;

    AssociationType(String value) {
        this.key = value;
    }

    public static AssociationType fromValue(@Nullable String value) {
        for (AssociationType type : values()) {
            if (type.key.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid association type: " + value);
    }
}
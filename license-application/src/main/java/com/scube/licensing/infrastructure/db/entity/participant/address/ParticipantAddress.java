package com.scube.licensing.infrastructure.db.entity.participant.address;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(
        name = ParticipantAddress.TABLE_NAME,
        indexes = {
                @Index(name = "idx_participant_address_participant_id", columnList = Participant.PARTICIPANT_ID),
                @Index(name = "idx_participant_address_address_id", columnList = Address.ADDRESS_ID),
                @Index(name = "idx_participant_address_participant_address_type_id", columnList = ParticipantAddressType.PARTICIPANT_ADDRESS_TYPE_ID)
        }
)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class ParticipantAddress extends BaseEntity {
    public static final String TABLE_NAME = "participant_address";
    public static final String PARTICIPANT_ADDRESS_ID = "participant_address_id";

    @Column(name = Participant.PARTICIPANT_ID, insertable = false, updatable = false)
    private Long participantId;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = Participant.PARTICIPANT_ID, nullable = false)
    private Participant participant;

    @Column(name = Address.ADDRESS_ID, insertable = false, updatable = false)
    private Long addressId;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = Address.ADDRESS_ID, nullable = false)
    private Address address;

    @Column(name = ParticipantAddressType.PARTICIPANT_ADDRESS_TYPE_ID, insertable = false, updatable = false)
    private Long participantAddressTypeId;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = ParticipantAddressType.PARTICIPANT_ADDRESS_TYPE_ID, nullable = false)
    private ParticipantAddressType participantAddressType;

    public boolean isHomeAddress() {
        return participantAddressType.isHomeAddress();
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}
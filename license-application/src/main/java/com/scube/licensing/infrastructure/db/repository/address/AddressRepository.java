package com.scube.licensing.infrastructure.db.repository.address;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import jakarta.validation.constraints.Size;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface AddressRepository extends AuditableEntityRepository<Address, Long> {
    Optional<Address> findFirstByStreetAddressIgnoreCaseAndStreetAddress2IgnoreCaseAndCityIgnoreCaseAndStateIgnoreCase(@Size(max = 255) String streetAddress, @Size(max = 255) String streetAddress2, @Size(max = 255) String city, @Size(max = 50) String state);

    @Query(nativeQuery = true, value = "SELECT * FROM get_parcels(:searchParcelNumber, :searchLotNumber, :searchBlockNumber, :searchSubdivision, :searchAddress, :searchAddress2, :searchCity, :searchState, :searchZip)")
    List<UUID> getSearchParcels(
            @Param("searchParcelNumber") @Size(max = 255) String searchParcelNumber,
            @Param("searchLotNumber") @Size(max = 255) String searchLotNumber,
            @Param("searchBlockNumber") @Size(max = 255) String searchBlockNumber,
            @Param("searchSubdivision") @Size(max = 255) String searchSubdivision,
            @Param("searchAddress") @Size(max = 255) String searchAddress,
            @Param("searchAddress2") @Size(max = 255) String searchAddress2,
            @Param("searchCity") @Size(max = 255) String searchCity,
            @Param("searchState") @Size(max = 255) String searchState,
            @Param("searchZip") @Size(max = 255) String searchZip
    );
}
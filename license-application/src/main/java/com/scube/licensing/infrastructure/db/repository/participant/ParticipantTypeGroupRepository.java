package com.scube.licensing.infrastructure.db.repository.participant;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantTypeGroup;
import jakarta.validation.constraints.Size;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ParticipantTypeGroupRepository extends AuditableEntityRepository<ParticipantTypeGroup, Long> {
    //find by ParticipantType.name and ParticipantGroup.name
    Optional<ParticipantTypeGroup> findByParticipantTypeNameAndParticipantGroupName(@Size(max = 255) String participantTypeName, @Size(max = 255) String participantGroupName);
}

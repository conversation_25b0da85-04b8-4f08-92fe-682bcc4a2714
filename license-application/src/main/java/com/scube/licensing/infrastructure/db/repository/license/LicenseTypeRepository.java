package com.scube.licensing.infrastructure.db.repository.license;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.license.type.LicenseType;
import jakarta.validation.constraints.Size;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LicenseTypeRepository extends AuditableEntityRepository<LicenseType, Long> {
    //find by name
    Optional<LicenseType> findByCodeIgnoreCase(@Size(max = 255) String name);

    boolean existsByCodeIgnoreCase(@Size(max = 255) String name);
}

package com.scube.licensing.infrastructure.db.repository.license;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.license.type.LicenseTypeSetting;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LicenseTypeSettingRepository extends AuditableEntityRepository<LicenseTypeSetting, Long> {
    Optional<LicenseTypeSetting> findByLicenseTypeId(Long licenseTypeId);
}

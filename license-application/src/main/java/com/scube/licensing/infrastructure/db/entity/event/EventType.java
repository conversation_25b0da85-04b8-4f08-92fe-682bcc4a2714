package com.scube.licensing.infrastructure.db.entity.event;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import com.scube.licensing.infrastructure.db.entity.profile_builder.ProfileType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(name = EventType.TABLE_NAME)
@Getter
@Setter
@Accessors(chain = true)
@Audited
public class EventType extends BaseEntity {
    public static final String TABLE_NAME = "event_type";
    public static final String EVENT_TYPE_ID = "event_type_id";
    public static final String E_CODE = "code";
    public static final String E_NAME = "name";

    @Size(max = 255)
    @Column(name = E_CODE, nullable = false, unique = true)
    private String code;

    @Size(max = 255)
    @Column(name = E_NAME, nullable = false)
    private String name;

    @Size(max = 255)
    private String description;

    @ManyToOne
    @JoinColumn(name = ProfileType.PROFILE_TYPE_ID)
    private ProfileType profileType;

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}

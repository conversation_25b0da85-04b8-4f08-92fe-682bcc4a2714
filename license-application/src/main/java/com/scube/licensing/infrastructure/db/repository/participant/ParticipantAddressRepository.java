package com.scube.licensing.infrastructure.db.repository.participant;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.participant.address.ParticipantAddress;
import org.springframework.stereotype.Repository;

@Repository
public interface ParticipantAddressRepository extends AuditableEntityRepository<ParticipantAddress, Long> {
}

package com.scube.licensing.infrastructure.db.entity.license.type;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(name = LicenseType.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class LicenseType extends BaseEntity {
    public static final String TABLE_NAME = "license_type";
    public static final String LICENSE_TYPE_ID = "license_type_id";
    public static final String C_NAME = "name";
    public static final String TYPE_KEY = "code";
    public static final String GROUP_NAME = "group_name";

    @Size(max = 50)
    @Column(unique = true, name = TYPE_KEY)
    private String code;

    @Size(max = 50)
    @Column(name = C_NAME)
    private String name;

    @Size(max = 250)
    private String description;

    @Size(max = 50)
    @Column(name = GROUP_NAME)
    private String groupName;

    @Size(max = 255)
    @Column(name = "fee_config_function")
    private String feeConfigFunction;

    @Size(max = 255)
    @Column(name = "duration_function")
    private String durationFunction;

    @Size(max = 255)
    @Column(name = "fee_preview_function")
    private String feePreviewFunction;

    public LicenseType(String code, String name, String description, String groupName) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.groupName = groupName;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}
package com.scube.licensing.infrastructure.db.entity.participant.contact;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(name = ContactTypeGroup.TABLE_NAME, uniqueConstraints = {@UniqueConstraint(name = "contact_type_group_unique", columnNames = {ContactGroup.CONTACT_GROUP_ID, ContactType.CONTACT_TYPE_ID})})
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class ContactTypeGroup extends BaseEntity {
    public static final String TABLE_NAME = "contact_type_group";
    public static final String CONTACT_TYPE_GROUP_ID = "contact_type_group_id";

    @Column(name = ContactGroup.CONTACT_GROUP_ID, nullable = false, insertable = false, updatable = false)
    private Long contactGroupId;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = ContactGroup.CONTACT_GROUP_ID, nullable = false)
    private ContactGroup contactGroup;

    @Column(name = ContactType.CONTACT_TYPE_ID, nullable = false, insertable = false, updatable = false)
    private Long contactTypeId;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = ContactType.CONTACT_TYPE_ID, nullable = false)
    private ContactType contactType;

    public ContactTypeGroup(ContactGroup contactGroup, ContactType contactType) {
        this.contactGroup = contactGroup;
        this.contactType = contactType;
    }

    public boolean isEmail() {
        return contactGroup.isEmail();
    }

    public boolean isPhone() {
        return contactGroup.isPhone();
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public boolean isPrimaryEmail() {
        return isEmail() && contactType.isPrimary();
    }

    public boolean isPrimaryPhone() {
        return isPhone() && contactType.isPrimary();
    }
}
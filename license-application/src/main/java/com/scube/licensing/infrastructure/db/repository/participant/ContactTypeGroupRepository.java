package com.scube.licensing.infrastructure.db.repository.participant;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.participant.contact.ContactTypeGroup;
import jakarta.validation.constraints.Size;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ContactTypeGroupRepository extends AuditableEntityRepository<ContactTypeGroup, Long> {
    //find by ContactType.name and ContactGroup.name
    Optional<ContactTypeGroup> findByContactTypeNameAndContactGroupName(@Size(max = 255) String contactTypeName, @Size(max = 255) String contactGroupName);
}
package com.scube.licensing.features.entity_group;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.entity_group.dtos.EntityGroupDto;
import com.scube.licensing.features.entity_group.dtos.EntityGroupRequest;
import com.scube.licensing.features.entity_group.dtos.GetAllEntityGroupResponse;
import com.scube.licensing.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/entity-group")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
@Validated
public class EntityGroupController {
    private final EntityGroupService entityGroupService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @SwaggerInfo(summary = "Create a group", description = "This is used to create a group.")
    @RolesAllowed(Permissions.EntityGroup.CREATE_ENTITY_GROUP)
    public EntityGroupDto createEntityGroup(@RequestBody EntityGroupRequest request) {
        return entityGroupService.createGroup(request);
    }

    @PostMapping(value = "{groupId}/add-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Add a file to a group", description = "This is used to add a file to a group.")
    @RolesAllowed(Permissions.EntityGroup.ADD_FILE)
    public List<DocumentDto> addFile(@PathVariable UUID groupId, @RequestParam Map<String, MultipartFile> files) {
        return entityGroupService.addFiles(groupId, files);
    }

    @PostMapping("{groupId}/add-association")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Add an association to a group", description = "This is used to add an association to a group.")
    @RolesAllowed(Permissions.EntityGroup.ADD_ASSOCIATION)
    public EntityGroupDto addAssociation(@PathVariable UUID groupId, @RequestBody EntityGroupRequest request) {
        return entityGroupService.addAssociation(groupId, request);
    }

    @PutMapping("{groupId}")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Update a group", description = "This is used to update a group.")
    @RolesAllowed(Permissions.EntityGroup.UPDATE_ENTITY_GROUP)
    public EntityGroupDto updateEntityGroup(@PathVariable UUID groupId, @RequestBody EntityGroupRequest request) {
        return entityGroupService.updateGroup(groupId, request);
    }

    @GetMapping("{entityType}/{entityId}/all")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Get all groups of an entity", description = "This is used to get all groups of an entity.")
    @RolesAllowed(Permissions.EntityGroup.GET_ALL_GROUPS)
    public GetAllEntityGroupResponse getAllGroups(@PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId) {
        return entityGroupService.getAll(entityType, entityId);
    }

    @GetMapping("{groupId}")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Get a group", description = "This is used to get a group.")
    @RolesAllowed(Permissions.EntityGroup.GET_GROUP)
    public EntityGroupDto getGroup(@PathVariable UUID groupId) {
        return entityGroupService.getGroup(groupId);
    }

    @DeleteMapping("{groupId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Delete a group", description = "This is used to delete a group.")
    @RolesAllowed(Permissions.EntityGroup.DELETE_GROUP)
    public void deleteGroup(@PathVariable UUID groupId) {
        entityGroupService.deleteGroup(groupId);
    }
}
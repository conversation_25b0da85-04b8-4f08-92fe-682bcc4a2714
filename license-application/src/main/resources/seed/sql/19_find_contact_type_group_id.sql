CREATE OR REPLACE FUNCTION find_contact_type_group_id
(
    fn_group_name character varying,
	fn_type_name character varying

)
RETURNS integer
AS $$
DECLARE
    result_id integer;
BEGIN
    SELECT contact_type_group_id
    INTO result_id
    FROM contact_type_group ctg
    inner join contact_type ct
        on ct.contact_type_id = ctg.contact_type_id
    inner join contact_group cg
        on cg.contact_group_id = ctg.contact_group_id
    where cg.g_name ILIKE fn_group_name
        and ct.t_name ILIKE fn_type_name;

    RETURN result_id;
END;
$$ LANGUAGE plpgsql;


--select * from find_contact_type_group_id('Phone', 'Home');
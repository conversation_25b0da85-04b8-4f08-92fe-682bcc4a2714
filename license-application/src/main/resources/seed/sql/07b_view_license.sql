--drop view if exists view_license;
CREATE OR REPLACE VIEW view_license AS
select
	ls.name AS license_status,
    ls.code AS license_status_code,
    lt.name AS license_type_name,
    lt.code AS license_type_code,
    lt.group_name AS license_type_group,
	l.*
from license l

inner join license_status ls
on ls.license_status_id = l.license_status_id

inner join license_type lt
on lt.license_type_id = l.license_type_id;
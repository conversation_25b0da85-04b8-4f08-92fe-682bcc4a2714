<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <!-- 1. Drop the existing foreign key constraint -->
    <changeSet author="david" id="3959869489659049-1">
        <addColumn tableName="license_activity_fee">
            <column name="paid_date" type="timestamp"/>
        </addColumn>
    </changeSet>
    <changeSet author="david" id="3959869489659049-2">
        <addColumn tableName="audit_log_license_activity_fee">
            <column name="paid_date" type="timestamp"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
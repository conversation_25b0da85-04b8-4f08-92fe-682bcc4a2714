<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="insertAdditionalProfileEvents2" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'individualReactivated', 'Individual Reactivated', 'Individual Reactivated',
            (select profile_type_id from profile_type where name = 'individual'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dogReactivated', 'Dog Reactivated', 'Dog Reactivated',
            (select profile_type_id from profile_type where name = 'dog'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'licenseCanceled', 'License Canceled', 'License Canceled',
            (select profile_type_id from profile_type where name = 'license'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'licenseCorrected', 'License Corrected', 'License Corrected',
            (select profile_type_id from profile_type where name = 'license'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dogFound', 'Dog Found', 'Dog Found',
            (select profile_type_id from profile_type where name = 'dog'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dogSetDangerousFalse', 'Dog Not Dangerous', 'Dog Not Dangerous',
            (select profile_type_id from profile_type where name = 'dog'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dogSetDangerousTrue', 'Dog Set Dangerous', 'Dog Set Dangerous',
            (select profile_type_id from profile_type where name = 'dog'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            update event_type
            set code = 'dogTransferOfOwnership'
            where code = 'transferOfOwnership';
        </sql>
    </changeSet>
    <changeSet id="eventType_insert_individualDeactivated" author="Ben">
    <sql>
        INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name, profile_type_id)
        values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'individualDeactivated', 'Individual Deactivated', 'Individual Deactivated',
        (select profile_type_id from profile_type where name = 'individual'))
        ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
    </sql>
    </changeSet>
</databaseChangeLog>

<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="david" id="drop_column_is_active">
        <dropColumn tableName="license_status">
            <column name="is_active"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="drop_column_is_approved">
        <dropColumn tableName="license_status">
            <column name="is_approved"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="drop_column_is_draft">
        <dropColumn tableName="license_status">
            <column name="is_draft"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="drop_column_is_expired">
        <dropColumn tableName="license_status">
            <column name="is_expired"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="drop_column_is_pending">
        <dropColumn tableName="license_status">
            <column name="is_pending"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="drop_column_is_rejected">
        <dropColumn tableName="license_status">
            <column name="is_rejected"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="drop_column_is_submitted">
        <dropColumn tableName="license_status">
            <column name="is_submitted"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="drop_column_is_closed">
        <dropColumn tableName="license_status">
            <column name="is_closed"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="add_column_code">
        <addColumn tableName="license_status">
            <column name="code" type="varchar(20)" defaultValue="PENDING">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="david" id="add_column_modifier">
        <addColumn tableName="license">
            <column name="modifier" type="varchar(20)"/>
        </addColumn>
    </changeSet>

    <!-- audit log-->
    <changeSet author="david" id="audit_log_drop_column_is_active">
        <dropColumn tableName="audit_log_license_status">
            <column name="is_active"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="audit_log_drop_column_is_approved">
        <dropColumn tableName="audit_log_license_status">
            <column name="is_approved"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="audit_log_drop_column_is_draft">
        <dropColumn tableName="audit_log_license_status">
            <column name="is_draft"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="audit_log_drop_column_is_expired">
        <dropColumn tableName="audit_log_license_status">
            <column name="is_expired"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="audit_log_drop_column_is_pending">
        <dropColumn tableName="audit_log_license_status">
            <column name="is_pending"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="audit_log_drop_column_is_rejected">
        <dropColumn tableName="audit_log_license_status">
            <column name="is_rejected"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="audit_log_drop_column_is_submitted">
        <dropColumn tableName="audit_log_license_status">
            <column name="is_submitted"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="audit_log_drop_column_is_closed">
        <dropColumn tableName="audit_log_license_status">
            <column name="is_closed"/>
        </dropColumn>
    </changeSet>
    <changeSet author="david" id="audit_log_add_column_code">
        <addColumn tableName="audit_log_license_status">
            <column name="code" defaultValue="pending" type="varchar(20)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="david" id="audit_log_add_column_modifier">
        <addColumn tableName="audit_log_license">
            <column name="modifier" type="varchar(255)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
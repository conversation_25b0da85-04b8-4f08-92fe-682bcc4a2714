<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="addProfileTypeToEventType-column" author="ben">
        <sql>
            DO '
            BEGIN
            IF NOT EXISTS (
            SELECT *
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ''event_type''
            AND COLUMN_NAME = ''profile_type_id''
            ) THEN
            ALTER TABLE event_type
            ADD profile_type_id BIGINT;
            END IF;
            END ';
        </sql>
    </changeSet>
    <changeSet id="addProfileTypeToEventType-insert-profile-form-builder" author="david">
        <sql>
            DO '
            BEGIN
            IF NOT EXISTS (
            select * from profile_form_builder
            ) THEN
            INSERT INTO profile_form_builder (created_by, created_date, last_modified_by, last_modified_date,
            description, name, conversion_reference) VALUES (''seed'', ''2023-12-03T19:57:21.125534'', ''seed'',
            ''2023-12-03T19:57:21.125534'', ''profile builder to display user profile'', ''individualUserProfile'',
            NULL);
            INSERT INTO profile_form_builder (created_by, created_date, last_modified_by, last_modified_date,
            description, name, conversion_reference) VALUES (''seed'', ''2023-12-03T19:57:21.84053'', ''seed'',
            ''2023-12-03T19:57:21.84053'', ''profile builder to display dog profile'', ''dogProfile'', NULL);
            INSERT INTO profile_form_builder (created_by, created_date, last_modified_by, last_modified_date,
            description, name, conversion_reference) VALUES (''seed'', ''2023-12-03T19:57:22.248028'', ''seed'',
            ''2023-12-03T19:57:22.248028'', ''profile builder to display parcel profile'', ''parcelProfile'', NULL);
            INSERT INTO profile_form_builder (created_by, created_date, last_modified_by, last_modified_date,
            description, name, conversion_reference) VALUES (''seed'', ''2023-12-03T19:57:22.616083'', ''seed'',
            ''2023-12-03T19:57:22.616083'', ''profile builder to display license profile'', ''licenseProfile'', NULL);
            END IF;
            END ';
        </sql>
    </changeSet>

    <changeSet id="addProfileTypeToEventType-insert-profile-types" author="david">
        <sql>
            DO '
            BEGIN
            IF NOT EXISTS (
            select * from profile_type
            ) THEN
            INSERT INTO profile_type (created_by, created_date, last_modified_by, last_modified_date, description, name,
            profile_form_builder_id, conversion_reference)
            VALUES (''seed'', ''2023-12-03T19:57:22.930646'', ''seed'', ''2023-12-03T19:57:22.930646'', ''individual
            profile type'', ''individual'', ''1'', NULL);
            INSERT INTO profile_type (created_by, created_date, last_modified_by, last_modified_date, description, name,
            profile_form_builder_id, conversion_reference)
            VALUES (''seed'', ''2023-12-03T19:57:22.93621'', ''seed'', ''2023-12-03T19:57:22.93621'', ''dog profile
            type'', ''dog'', ''2'', NULL);
            INSERT INTO profile_type (created_by, created_date, last_modified_by, last_modified_date, description, name,
            profile_form_builder_id, conversion_reference)
            VALUES (''seed'', ''2023-12-03T19:57:22.940894'', ''seed'', ''2023-12-03T19:57:22.940894'', ''parcel profile
            type'', ''parcel'', ''3'', NULL);
            INSERT INTO profile_type (created_by, created_date, last_modified_by, last_modified_date, description, name,
            profile_form_builder_id, conversion_reference)
            VALUES (''seed'', ''2023-12-03T19:57:22.946157'', ''seed'', ''2023-12-03T19:57:22.946157'', ''license
            profile type'', ''license'', ''4'', NULL);
            END IF;
            END ';
        </sql>
    </changeSet>
    <changeSet id="addProfileTypeToEventType-sql" author="ben">
        <sql>
            UPDATE event_type
            SET profile_type_id = (select profile_type_id from profile_type where name = 'dog')
            WHERE code = 'dangerousDog';
            UPDATE event_type
            SET profile_type_id = (select profile_type_id from profile_type where name = 'dog')
            WHERE code = 'transferOfOwnership';
            UPDATE event_type
            SET profile_type_id = (select profile_type_id from profile_type where name = 'individual')
            WHERE code = 'addressChange';
        </sql>
    </changeSet>
    <changeSet id="addProfileTypeToEventType-not-null" author="ben">
        <sql>
            -- Check if the NOT NULL constraint exists before adding it
            DO '
            BEGIN
            IF NOT EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
            WHERE CONSTRAINT_TYPE = ''CHECK''
            and table_name = ''event_type''
            AND CONSTRAINT_NAME ILIKE ''%10_not_null''
            ) THEN
            ALTER TABLE event_type
            ALTER COLUMN profile_type_id SET NOT NULL;
            END IF;
            END ';
        </sql>

        <sql>
            -- Check if the foreign key constraint exists before adding it
            DO '
            BEGIN
            IF NOT EXISTS (
            SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
            WHERE CONSTRAINT_TYPE = ''FOREIGN KEY'' AND CONSTRAINT_NAME = ''fk_event_type_profile_type_id''
            ) THEN
            ALTER TABLE event_type
            ADD CONSTRAINT fk_event_type_profile_type_id
            FOREIGN KEY (profile_type_id)
            REFERENCES profile_type(profile_type_id);
            END IF;
            END ';

        </sql>
    </changeSet>
    <changeSet id="addProfileTypeIdToAuditLogEventType" author="ben">
        <addColumn tableName="audit_log_event_type">
            <column name="profile_type_id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet id="insertDeceasedDogEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dogDeceased', 'Dog Deceased', 'Dog
            Deceased',
            (select profile_type_id from profile_type where name = 'dog'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="insertDeceasedIndividualEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'individualDeceased', 'Individual
            Deceased', 'Individual Deceased',
            (select profile_type_id from profile_type where name = 'individual'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="insertLostDogEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dogLost', 'Dog Lost', 'Dog Lost',
            (select profile_type_id from profile_type where name = 'dog'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="insertRelinquishedDogEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dogRelinquished', 'Dog Relinquished',
            'Dog Relinquished',
            (select profile_type_id from profile_type where name = 'dog'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="insertIndividualMovedOutJurisEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'individualMovedOutsideJurisdiction',
            'Individual Moved outside Jurisdiction', 'Individual Moved outside Jurisdiction',
            (select profile_type_id from profile_type where name = 'individual'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="insertLicenseCorrectionEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'licenseCorrection', 'License
            Correction', 'License Correction',
            (select profile_type_id from profile_type where name = 'license'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="insertLicenseCanceledEventType" author="Ben">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'licenseCanceled', 'License Canceled',
            'License Canceled',
            (select profile_type_id from profile_type where name = 'license'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="addParticipantStatusTable" author="ben">
        <createTable tableName="participant_status">
            <column autoIncrement="true" name="participant_status_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="participant_status_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="participant_group_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="participant_group_id, name" constraintName="uk_participant_status_code"
                             tableName="participant_status"/>
        <addForeignKeyConstraint constraintName="fk_participant_status_participant_group_id"
                                 baseTableName="participant_status"
                                 baseColumnNames="participant_group_id"
                                 referencedTableName="participant_group"
                                 referencedColumnNames="participant_group_id"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="addAuditLogParticipantStatusTable">
        <createTable tableName="audit_log_participant_status">
            <column name="participant_status_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_participant_status_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_participant_status_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="participant_group_id" type="bigint"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="code" type="VARCHAR(255)"/>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_participant_status"
                                 constraintName="audit_log_participant_status_rev_id_fk" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>

    <changeSet id="insertStatusesIntoParticipantStatus-participant-group" author="David">
        <sql>
            DO '
            BEGIN
            IF NOT EXISTS (
            select * from participant_group
            ) THEN
            INSERT INTO participant_group (created_by, created_date, last_modified_by, last_modified_date, description,
            name, conversion_reference) VALUES (''seed'', ''2023-12-03T19:57:03.322767'', ''seed'',
            ''2023-12-03T19:57:03.322767'', ''Person participant group'', ''Individual'', NULL);
            INSERT INTO participant_group (created_by, created_date, last_modified_by, last_modified_date, description,
            name, conversion_reference) VALUES (''seed'', ''2023-12-03T19:57:03.343831'', ''seed'',
            ''2023-12-03T19:57:03.343831'', ''Organization participant group'', ''Organization'', NULL);
            INSERT INTO participant_group (created_by, created_date, last_modified_by, last_modified_date, description,
            name, conversion_reference) VALUES (''seed'', ''2023-12-03T19:57:03.354907'', ''seed'',
            ''2023-12-03T19:57:03.354907'', ''Dog participant group'', ''Dog'', NULL);
            END IF;
            END ';
        </sql>
    </changeSet>

    <changeSet id="insertStatusesIntoParticipantStatus" author="ben">
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Active', 'ACTIVE',
            (select participant_group_id from participant_group where name = 'Individual'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Inactive', 'INACTIVE',
            (select participant_group_id from participant_group where name = 'Individual'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Active', 'ACTIVE',
            (select participant_group_id from participant_group where name = 'Dog'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Inactive', 'INACTIVE',
            (select participant_group_id from participant_group where name = 'Dog'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Relinquished', 'ACTIVE',
            (select participant_group_id from participant_group where name = 'Dog'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Lost', 'ACTIVE',
            (select participant_group_id from participant_group where name = 'Dog'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Deceased', 'INACTIVE',
            (select participant_group_id from participant_group where name = 'Dog'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Moved Out Of Jurisdiction', 'INACTIVE',
            (select participant_group_id from participant_group where name = 'Dog'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Deceased', 'INACTIVE',
            (select participant_group_id from participant_group where name = 'Individual'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Moved Out Of Jurisdiction', 'INACTIVE',
            (select participant_group_id from participant_group where name = 'Individual'))
            ON CONFLICT ON CONSTRAINT uk_participant_status_code DO NOTHING;
        </sql>
    </changeSet>
    <changeSet id="addParticipantStatusToPartipantTable" author="ben">
        <addColumn tableName="participant">
            <column name="participant_status_id" type="bigint"/>
        </addColumn>
        <sql>
            UPDATE participant
            SET participant_status_id = (select participant_status_id from participant_status where name = 'Active'
            AND participant_group_id = (select participant_group_id from participant_group where name = 'Dog'))
            WHERE participant_type_group_id in (
            select participant_type_group_id from participant_type_group
            inner join participant_group
            on participant_group.participant_group_id = participant_type_group.participant_group_id
            where participant_group.name = 'Dog');
        </sql>
        <sql>
            UPDATE participant
            SET participant_status_id = (select participant_status_id from participant_status where name = 'Active'
            AND participant_group_id = (select participant_group_id from participant_group where name = 'Individual'))
            WHERE participant_type_group_id in (
            select participant_type_group_id from participant_type_group
            inner join participant_group
            on participant_group.participant_group_id = participant_type_group.participant_group_id
            where participant_group.name = 'Individual');
        </sql>
        <addNotNullConstraint tableName="participant"
                              columnName="participant_status_id"
                              columnDataType="bigint"/>
        <addForeignKeyConstraint constraintName="fk_participant_participant_status_id"
                                 baseTableName="participant"
                                 baseColumnNames="participant_status_id"
                                 referencedTableName="participant_status"
                                 referencedColumnNames="participant_status_id"/>
        <addColumn tableName="audit_log_participant">
            <column name="participant_status_id" type="bigint"/>
        </addColumn>
    </changeSet>
    <changeSet id="updateColumn_participant_status_code_relinquished" author="David">
        <sql>
            update participant_status
            set code = 'INACTIVE'
            where name = 'Relinquished';
        </sql>
    </changeSet>
    <changeSet id="updateColumn_audit_log_participant_status_code_relinquished" author="David">
        <sql>
            update audit_log_participant_status
            set code = 'INACTIVE'
            where name = 'Relinquished';
        </sql>
    </changeSet>
</databaseChangeLog>

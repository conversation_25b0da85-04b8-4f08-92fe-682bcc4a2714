<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="Ben (generated)" id="1707834709640-1">
        <createTable tableName="document">
            <column autoIncrement="true" name="document_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="document_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="events" type="JSONB"/>
            <column name="content_type" type="VARCHAR(255)"/>
            <column name="deleted_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="document_service_uuid" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="file_name" type="VARCHAR(255)"/>
            <column name="is_deleted" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="size" type="BIGINT"/>
            <column name="url" type="VARCHAR(255)"/>
            <column name="document_type_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1707834709640-2">
        <createTable tableName="document_type">
            <column autoIncrement="true" name="document_type_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="document_type_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="group_name" type="VARCHAR(255)"/>
            <column name="key" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1707834709640-3">
        <addUniqueConstraint columnNames="entity_id" constraintName="uk_document_entity_id" tableName="document"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1707834709640-4">
        <addUniqueConstraint columnNames="key" constraintName="uk_document_type_key" tableName="document_type"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1707834709640-5">
        <createTable tableName="audit_log_document">
            <column name="document_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_document_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_document_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="entity_id" type="VARCHAR(36)"/>
            <column name="events" type="JSONB"/>
            <column name="content_type" type="VARCHAR(255)"/>
            <column name="deleted_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="document_service_uuid" type="UUID"/>
            <column name="file_name" type="VARCHAR(255)"/>
            <column name="is_deleted" type="BOOLEAN"/>
            <column name="size" type="BIGINT"/>
            <column name="url" type="VARCHAR(255)"/>
            <column name="document_type_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1707834709640-6">
        <createTable tableName="audit_log_document_type">
            <column name="document_type_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_document_type_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_document_type_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="group_name" type="VARCHAR(255)"/>
            <column name="key" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1707834709640-7">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_document" constraintName="fk2tuw42qtjiwvjokoepb9ttm1p" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1707834709640-8">
        <addForeignKeyConstraint baseColumnNames="document_type_id" baseTableName="document" constraintName="fke2no7rafy2kby8devwkuyfak3" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="document_type_id" referencedTableName="document_type" validate="true"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1707834709640-10">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_document_type" constraintName="fknxlnvj9dsaqdn6htvaj5x4bbm" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
</databaseChangeLog>

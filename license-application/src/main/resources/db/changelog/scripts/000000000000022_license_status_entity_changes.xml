<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr" id="1696517522101-1">
        <sqlFile path="drop_views_functions.sql"
                 relativeToChangelogFile="true" splitStatements="false"/>
    </changeSet>

    <changeSet author="davidr (generated)" id="1696517522101-2">
        <addColumn tableName="audit_log_address">
            <column name="entity_id" type="varchar(36 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-3">
        <addColumn tableName="audit_log_custom_entity_instance">
            <column name="entity_id" type="varchar(36 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-4">
        <addColumn tableName="audit_log_license">
            <column name="entity_id" type="varchar(36 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-5">
        <addColumn tableName="audit_log_participant">
            <column name="entity_id" type="varchar(36 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-6">
        <addColumn tableName="audit_log_tenant">
            <column name="entity_id" type="varchar(36 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-7">
        <addColumn tableName="audit_log_license_status">
            <column name="is_closed" type="bool"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-8">
        <addColumn tableName="license_status">
            <column name="is_closed" type="bool" defaultValue="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-9">
        <modifyDataType columnName="city" newDataType="varchar(255)" tableName="audit_log_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-10">
        <modifyDataType columnName="house_number" newDataType="varchar(255)" tableName="audit_log_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-11">
        <modifyDataType columnName="state" newDataType="varchar(255)" tableName="audit_log_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696517522101-12">
        <modifyDataType columnName="zip" newDataType="varchar(255)" tableName="audit_log_address"/>
    </changeSet>
</databaseChangeLog>

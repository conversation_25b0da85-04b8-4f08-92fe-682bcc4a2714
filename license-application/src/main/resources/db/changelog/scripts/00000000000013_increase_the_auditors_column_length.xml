<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="removeViews">
        <sql splitStatements="true">
            DROP VIEW IF EXISTS view_pivoted_license;
            DROP VIEW IF EXISTS view_license_for_reports;
            DROP VIEW IF EXISTS view_dog_licenses;
            DROP VIEW IF EXISTS view_participant;
            DROP VIEW IF EXISTS view_contact;
            DROP VIEW IF EXISTS view_custom_field_value;
            drop function if exists get_participants;
            drop function if exists get_dogs;
            drop function if exists get_parcels;
            drop function if exists get_licenses;
        </sql>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-1">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-2">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-3">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="association"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-4">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="contact"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-5">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="contact_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-6">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="contact_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-7">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-8">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-9">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-10">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-11">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-12">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-13">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="license_activity"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-14">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-15">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="license_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-16">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-17">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-18">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-19">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-20">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-21">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-22">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-23">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-24">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-25">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-26">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-27">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-28">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-29">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-30">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-31">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-32">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-33">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-34">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-35">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-36">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-37">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-38">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="participant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-39">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="participant_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-40">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-41">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="participant_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-42">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="participant_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-43">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-44">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="profile_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-45">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-46">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-47">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="profile_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-48">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="profile_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-49">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="profile_form_section_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-50">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="profile_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-51">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-52">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-53">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-54">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-55">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-56">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-57">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-58">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="table_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-59">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="tenant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-60">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-61">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-62">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="association"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-63">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-64">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-65">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-66">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-67">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-68">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-69">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-70">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-71">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-72">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-73">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-74">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-75">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-76">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-77">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-78">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-79">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-80">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-81">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-82">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-83">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-84">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-85">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-86">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-87">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-88">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-89">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-90">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-91">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-92">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-93">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-94">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-95">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-96">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-97">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-98">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-99">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-100">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-101">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-102">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-103">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-104">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-105">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-106">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-107">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_section_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-108">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-109">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-110">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-111">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-112">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-113">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-114">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-115">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-116">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-117">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="tenant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-118">
        <modifyDataType columnName="enqueued_at" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-119">
        <modifyDataType columnName="issued_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-120">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-121">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-122">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="association"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-123">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="contact"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-124">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="contact_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-125">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="contact_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-126">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-127">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-128">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-129">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-130">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-131">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-132">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="license_activity"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-133">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-134">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="license_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-135">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-136">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-137">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-138">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-139">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-140">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-141">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-142">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-143">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-144">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-145">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-146">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-147">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-148">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-149">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-150">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-151">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-152">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-153">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-154">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-155">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-156">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-157">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="participant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-158">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="participant_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-159">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-160">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="participant_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-161">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="participant_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-162">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-163">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="profile_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-164">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-165">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-166">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="profile_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-167">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="profile_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-168">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="profile_form_section_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-169">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="profile_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-170">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-171">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)"
                        tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-172">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-173">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-174">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-175">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-176">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-177">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="table_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-178">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="tenant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-179">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-180">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-181">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="association"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-182">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-183">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-184">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-185">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-186">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-187">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-188">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-189">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-190">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-191">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_activity_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-192">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-193">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-194">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-195">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-196">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-197">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-198">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-199">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-200">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-201">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-202">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-203">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-204">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-205">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-206">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-207">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-208">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-209">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-210">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-211">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-212">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-213">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-214">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-215">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-216">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-217">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-218">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-219">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-220">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-221">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-222">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-223">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-224">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-225">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-226">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_section_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-227">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-228">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-229">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-230">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-231">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-232">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-233">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-234">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-235">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-236">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="tenant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-237">
        <modifyDataType columnName="last_touched" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-238">
        <addNotNullConstraint columnDataType="varchar(255)" columnName="payment_status" tableName="license_activity_fee"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-239">
        <modifyDataType columnName="processing_started" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-240">
        <modifyDataType columnName="valid_from_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695249358987-241">
        <modifyDataType columnName="valid_to_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
</databaseChangeLog>

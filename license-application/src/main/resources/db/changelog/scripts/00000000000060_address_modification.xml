<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="createFunction_remove_any_extra_spaces_and_trim" author="David">
        <sql>
            CREATE OR REPLACE FUNCTION remove_any_extra_spaces_and_trim(input_text text)
            RETURNS text AS '
            BEGIN
            RETURN TRIM(
            REGEXP_REPLACE(
            REGEXP_REPLACE(
            REGEXP_REPLACE(input_text, ''(,\s*,)+'', '', '', ''g''), -- Replace repeated commas with a single comma and space
            ''\s{2,}'', '' '', ''g'' -- Replace multiple spaces with a single space
            ),
            '',\s*$'', '''' -- Remove trailing comma and space
            )
            );
            END;
            ' LANGUAGE plpgsql;
        </sql>
    </changeSet>
    <changeSet id="address_addTrigger_format_full_address" author="David">
        <sql>
            update address
            set full_address = remove_any_extra_spaces_and_trim(coalesce(street_address, '') || ', ' ||
            coalesce(street_address_2, '') || ', ' || coalesce(city, '') || ', ' || coalesce(state, '') || ' ' ||
            coalesce(zip, ''))
        </sql>
    </changeSet>
    <changeSet id="address_createFunction_updateFullAddress" author="David">
        <sql>
            CREATE OR REPLACE FUNCTION update_full_address()
            RETURNS TRIGGER AS '
            BEGIN
            NEW.full_address = remove_any_extra_spaces_and_trim(coalesce(NEW.street_address, '''') || '', '' ||
            coalesce(NEW.street_address_2, '''') || '', '' || coalesce(NEW.city, '''') || '', '' || coalesce(NEW.state,
            '''') || '' '' || coalesce(NEW.zip, ''''));
            RETURN NEW;
            END;
            ' LANGUAGE plpgsql;
        </sql>
    </changeSet>
    <changeSet id="address_createTrigger_updateFullAddress" author="David">
        <sql>
            CREATE or REPLACE TRIGGER update_full_address_trigger
            BEFORE INSERT OR UPDATE ON address
            FOR EACH ROW
            EXECUTE FUNCTION update_full_address();
        </sql>
    </changeSet>
</databaseChangeLog>
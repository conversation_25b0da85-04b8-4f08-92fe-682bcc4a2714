<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1690999894572-1">
        <createTable tableName="license_type_setting">
            <column autoIncrement="true" name="license_type_setting_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="license_type_setting_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="license_type_id" type="BIGINT"/>
            <column name="on_form_submit_license_status_id" type="BIGINT"/>
            <column name="on_initial_form_create_license_status_id" type="BIGINT"/>
            <column name="partial_save_license_status_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-2">
        <addUniqueConstraint columnNames="license_type_id" constraintName="uk_err5na2qy2em0st12br61pny"
                             tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-3">
        <addForeignKeyConstraint baseColumnNames="partial_save_license_status_id" baseTableName="license_type_setting"
                                 constraintName="fk1u2x4kmkveubusq9202iwdqr1" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="license_status_id" referencedTableName="license_status"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-4">
        <addForeignKeyConstraint baseColumnNames="on_initial_form_create_license_status_id"
                                 baseTableName="license_type_setting" constraintName="fk26fyaoastg5xfdbs5wfcsbnxh"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="license_status_id" referencedTableName="license_status"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-5">
        <addForeignKeyConstraint baseColumnNames="on_form_submit_license_status_id" baseTableName="license_type_setting"
                                 constraintName="fkbouamjmxvnfsmx7ornn10mrj7" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="license_status_id" referencedTableName="license_status"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-6">
        <addForeignKeyConstraint baseColumnNames="license_type_id" baseTableName="license_type_setting"
                                 constraintName="fkinfote72077rdf7syw0twrxh2" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="license_type_id" referencedTableName="license_type"
                                 validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-7">
        <dropForeignKeyConstraint baseTableName="license_type" constraintName="fk3s1b50u5779rh87thtf3ig5p3"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-8">
        <dropForeignKeyConstraint baseTableName="license_type" constraintName="fk64bti2k0sl85fh8nnjcurhwnw"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-9">
        <dropForeignKeyConstraint baseTableName="license_type" constraintName="fkdglia2hda2h2krrh7rqgj2tot"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-11">
        <dropColumn columnName="on_form_submit_license_status_id" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-12">
        <dropColumn columnName="on_initial_form_create_license_status_id" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1690999894572-13">
        <dropColumn columnName="partial_save_license_status_id" tableName="license_type"/>
    </changeSet>
</databaseChangeLog>

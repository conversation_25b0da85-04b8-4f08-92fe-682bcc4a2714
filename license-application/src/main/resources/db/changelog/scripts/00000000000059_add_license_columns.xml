<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="addColumn_approvedBy_license" author="David">
        <addColumn tableName="license">
            <column name="approved_by" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_approvedBy_audit_log_license" author="David">
        <addColumn tableName="audit_log_license">
            <column name="approved_by" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="addColumn_issuedDate_license_activity" author="David">
        <addColumn tableName="license_activity">
            <column name="issued_date" type="timestamp">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="addColumn_issuedDate_audit_log_license_activity" author="David">
        <addColumn tableName="audit_log_license_activity">
            <column name="issued_date" type="timestamp">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="updateTable_licenseIssuedDate_license" author="David">
        <sql>
            <![CDATA[
                update license
                set issued_date = approved_date
                where issued_date::date < approved_date::date;
            ]]>
        </sql>
    </changeSet>

    <changeSet id="updateTable_licenseIssuedDate_audit_log_license" author="David">
        <sql>
            <![CDATA[
                update audit_log_license
                set issued_date = approved_date
                where issued_date::date < approved_date::date;
            ]]>
        </sql>
    </changeSet>

    <changeSet id="updateTable_lastModifiedToIssuedDate_license_activity" author="David">
        <sql>
            <![CDATA[
                update license_activity
                set issued_date = last_modified_date
            ]]>
        </sql>
    </changeSet>
    <changeSet id="updateTable_lastModifiedToIssuedDate_audit_log_license_activity" author="David">
        <sql>
            <![CDATA[
                update audit_log_license_activity
                set issued_date = last_modified_date
            ]]>
        </sql>
    </changeSet>

    <changeSet id="updateTable_issuedDateToNull_license_activity4" author="David">
        <sql>
            <![CDATA[
                update license_activity la
                    set issued_date = null
                from license l
                inner join license_status ls
                    on ls.license_status_id = l.license_status_id
                    and ls.code not ilike 'draft'
                where la.issued_date::date >= '05/01/2024'
                and ls.code != 'ACTIVE'
                and l.license_id = la.license_id;
            ]]>
        </sql>
    </changeSet>
    <changeSet id="updateTable_issuedDateToNull_audit_log_license_activity4" author="David">
        <sql>
            <![CDATA[
                update audit_log_license_activity la
                    set issued_date = null
                from license l
                inner join license_status ls
                on ls.license_status_id = l.license_status_id
                and ls.code not ilike 'draft'
                where la.issued_date::date >= '05/01/2024'
                and ls.code != 'ACTIVE'
                and l.license_id = la.license_id;
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog>
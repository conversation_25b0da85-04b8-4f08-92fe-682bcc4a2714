<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.6.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet author="Ben (generated)" id="1692980198682-1">
        <createTable tableName="app_properties">
            <column name="uuid" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="app_properties_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(250)"/>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1692980198682-2">
        <addUniqueConstraint columnNames="name" constraintName="uk_crbdhq8ruhgfgi1cq0mufvm7v" tableName="app_properties"/>
    </changeSet>
    <changeSet author="ben" id="1690815186088-17">
        <sql>
            INSERT INTO app_properties (name, value, description, created_date, last_modified_date, uuid)
            VALUES
            ('reminder-processing-cron', '0 0 13 * * *', 'The reminder cron schedule. Currently set to once per day at 1pm',
            current_date, current_date, gen_random_uuid()),
            ('delinquency-processing-cron', '0 0 13 * * *', 'The delinquency cron schedule. Currently set to once per day at 1pm',
            current_date, current_date, gen_random_uuid()),
            ('reminder-frequencies', '30,60', 'Values correspond to how many days before license expires to send reminders',
            current_date, current_date, gen_random_uuid())
            ON CONFLICT ON CONSTRAINT uk_crbdhq8ruhgfgi1cq0mufvm7v DO NOTHING;
        </sql>
    </changeSet>
</databaseChangeLog>

<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="David" id="add-participant-approved">
        <addColumn tableName="participant">
            <column name="approved" type="BOOLEAN" defaultValue="false"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="add-approved-date">
        <addColumn tableName="participant">
            <column name="approved_date" type="TIMESTAMP"/>
        </addColumn>
    </changeSet>

    <!--    for audit log-->
    <changeSet author="David" id="add-audit-log-participant-approved">
        <addColumn tableName="audit_log_participant">
            <column name="approved" type="BOOLEAN" defaultValue="false"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="add-audit-log-participant-approved-date">
        <addColumn tableName="audit_log_participant">
            <column name="approved_date" type="TIMESTAMP"/>
        </addColumn>
    </changeSet>

    <!--    update all the existing participant to approved true-->
    <changeSet author="David" id="update-participant-approved">
        <sql>
            UPDATE participant SET approved = true, approved_date = created_date;
        </sql>
    </changeSet>
    <changeSet author="David" id="update-audit log participant-approved">
        <sql>
            UPDATE audit_log_participant SET approved = true, approved_date = last_modified_date;
        </sql>
    </changeSet>
    <!--    set the not null constraint on the approved column-->
    <changeSet author="David" id="set-participant-approved-not-null-constraint">
        <addNotNullConstraint tableName="participant" columnName="approved" columnDataType="BOOLEAN"
                              defaultNullValue="false"/>
    </changeSet>
    <changeSet author="David" id="set-audit-log-participant-approved-not-null-constraint">
        <addNotNullConstraint tableName="audit_log_participant" columnName="approved" columnDataType="BOOLEAN"
                              defaultNullValue="false"/>
    </changeSet>

    <!--    licenses -->
    <changeSet author="David" id="add-license-approved">
        <addColumn tableName="license">
            <column name="approved" type="BOOLEAN" defaultValue="false"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="add-license-approved-date">
        <addColumn tableName="license">
            <column name="approved_date" type="TIMESTAMP"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="add-audit-log-license-approved">
        <addColumn tableName="audit_log_license">
            <column name="approved" type="BOOLEAN" defaultValue="false"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="add-audit-log-license-approved-date">
        <addColumn tableName="audit_log_license">
            <column name="approved_date" type="TIMESTAMP"/>
        </addColumn>
    </changeSet>

    <!--    update approveds of existing to true-->
    <changeSet author="David" id="update-license-approved">
        <sql>
            UPDATE license SET approved = true, approved_date = issued_date;
        </sql>
    </changeSet>
    <changeSet author="David" id="update-audit-log-license-approved">
        <sql>
            UPDATE audit_log_license SET approved = true, approved_date = issued_date;
        </sql>
    </changeSet>

    <!--    set the not null constraint on the approved column-->
    <changeSet author="David" id="set-not-null-constraint-license">
        <addNotNullConstraint tableName="license" columnName="approved" columnDataType="BOOLEAN"
                              defaultNullValue="false"/>
    </changeSet>
    <changeSet author="David" id="set-not-null-constraint-audit-log-license">
        <addNotNullConstraint tableName="audit_log_license" columnName="approved" columnDataType="BOOLEAN"
                              defaultNullValue="false"/>
    </changeSet>

    <changeSet author="David" id="add-license-denied-comment">
        <addColumn tableName="license">
            <column name="denied_comment" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="add-audit-log-license-denied-comment">
        <addColumn tableName="audit_log_license">
            <column name="denied_comment" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr" id="drop_view_pivoted_license_for_denied_comment_modifier_changes">
        <sql>
            DROP VIEW IF EXISTS view_pivoted_license;
        </sql>
    </changeSet>
    <changeSet author="David" id="license_changeDataType_text_denied_comment">
        <modifyDataType tableName="license" columnName="denied_comment" newDataType="text"/>
    </changeSet>
    <changeSet author="David" id="audit_log_license_changeDataType_text_denied_comment">
        <modifyDataType tableName="audit_log_license" columnName="denied_comment" newDataType="text"/>
    </changeSet>
    <changeSet author="David" id="license_changeDataType_text_modifier">
        <modifyDataType tableName="license" columnName="modifier" newDataType="text"/>
    </changeSet>
    <changeSet author="David" id="audit_log_license_changeDataType_text_modifier">
        <modifyDataType tableName="audit_log_license" columnName="modifier" newDataType="text"/>
    </changeSet>

    <changeSet author="David" id="add-dog-approval-fields">
        <sql>
            DO '
            DECLARE
            var_participant_id BIGINT;
            cur CURSOR FOR
            select distinct participant_id from view_participant vp
            where vp.group_name ILIKE ''dog'';
            BEGIN
            OPEN cur;
            LOOP
            FETCH NEXT FROM cur INTO var_participant_id;
            EXIT WHEN NOT FOUND;

            PERFORM insert_update_custom_field_value(''participant'', ''basicInfoApproved'', var_participant_id,
            ''boolean'', trim(''true''), ''system'', '''');
            PERFORM insert_update_custom_field_value(''participant'', ''behaviorApproved'', var_participant_id,
            ''boolean'', trim(''true''), ''system'', '''');
            PERFORM insert_update_custom_field_value(''participant'', ''physicalCharacteristicsApproved'',
            var_participant_id, ''boolean'', trim(''true''), ''system'', '''');
            PERFORM insert_update_custom_field_value(''participant'', ''vaccineApproved'', var_participant_id,
            ''boolean'', trim(''true''), ''system'', '''');
            PERFORM insert_update_custom_field_value(''participant'', ''insuranceApproved'', var_participant_id,
            ''boolean'', trim(''true''), ''system'', '''');

            END LOOP;
            CLOSE cur;
            END ';
        </sql>
    </changeSet>

    <changeSet author="David" id="add-individual-approval-fields">
        <sql>
            DO '
            DECLARE
            var_participant_id BIGINT;
            cur CURSOR FOR
            select distinct participant_id from view_participant vp
            where vp.group_name ILIKE ''individual'';
            BEGIN
            OPEN cur;
            LOOP
            FETCH NEXT FROM cur INTO var_participant_id;
            EXIT WHEN NOT FOUND;

            PERFORM insert_update_custom_field_value(''participant'', ''basicInfoApproved'', var_participant_id,
            ''boolean'', trim(''true''), ''system'', '''');
            PERFORM insert_update_custom_field_value(''participant'', ''addressApproved'', var_participant_id,
            ''boolean'', trim(''true''), ''system'', '''');

            END LOOP;
            CLOSE cur;
            END ';
        </sql>
    </changeSet>

</databaseChangeLog>
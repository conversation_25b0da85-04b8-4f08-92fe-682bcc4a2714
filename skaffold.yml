apiVersion: skaffold/v4beta6
kind: Config
metadata:
  name: backend
build:
  artifacts:
    - image: service_document
      context: ../Service_Document
      jib:
        project: com.scube.document:document-application

manifests:
  rawYaml:
    - deployment.yml

profiles:
  - name: local
    activation:
      - kubeContext: docker-desktop
    build:
      local:
        push: false
    deploy:
      kubectl:
        defaultNamespace: backend


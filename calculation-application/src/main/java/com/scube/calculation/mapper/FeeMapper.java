package com.scube.calculation.mapper;


import com.scube.calculation.dto.UpsertFeeRequest;
import com.scube.calculation.dto.fee.FeeDto;
import com.scube.calculation.model.Fee;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class FeeMapper {
    public abstract FeeDto toDto(Fee fee);
    public abstract List<FeeDto> toDto(List<Fee> fees);
    public abstract Fee toEntity(FeeDto feeDto);
    public abstract List<Fee> toEntity(List<FeeDto> feeDtos);
    public abstract Fee toEntityFromRequest(UpsertFeeRequest feeRequest);
    public abstract List<Fee> toEntityFromRequest(List<UpsertFeeRequest> feeDtos);
}
package com.scube.calculation.config;

import com.scube.auth.library.ITokenService;
import com.scube.calculation.repository.CartRepository;
import com.scube.calculation.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Slf4j
@RequiredArgsConstructor
public class MySecurityExpressionRoot extends MyMethodSecurityExpressionRoot {
    private final ITokenService tokenService;
    private final CartRepository cartRepository;
    private final OrderRepository orderRepository;

    @Override
    public ITokenService getTokenService() {
        return tokenService;
    }

    public boolean hasPermission(String permission) {
        log.info("Checking if user: {} has permission: {}", getLoggedInUsername(), permission);
        return hasAuthority(permission);
    }

    public boolean isOwnerOfCart(UUID cartId) {
        log.info("Checking if user: {} is owner of cart: {}", getLoggedInUsername(), cartId);
        var userId = getLoggedInUserId();
        var result = cartRepository.existsByIdAndUserId(cartId, userId);
        log.info("User: {} is owner of cart: {}? {}", getLoggedInUsername(), cartId, result);
        return result;
    }

    public boolean isOwnerOfOrder(UUID orderId) {
        log.info("Checking if user: {} is owner of order: {}", getLoggedInUsername(), orderId);
        var userId = getLoggedInUserId();
        // DR: This is causing issues when the resident create a license and the clerk pays for it
        //     This order belongs to the clerk and not the resident.
        //return orderRepository.existsByIdAndUserId(orderId, userId);
        return true;
    }
}
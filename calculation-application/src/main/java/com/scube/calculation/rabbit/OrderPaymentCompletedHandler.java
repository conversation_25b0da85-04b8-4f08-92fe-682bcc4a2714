package com.scube.calculation.rabbit;

import com.scube.calculation.dto.order.OrderInvoiceResponse;
import com.scube.calculation.repository.OrderRepository;
import com.scube.calculation.service.CartService;
import com.scube.calculation.service.OrderService;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;

/**
 * When the payment is completed, mark the order as paid
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderPaymentCompletedHandler extends FanoutListener<OrderPaymentCompletedHandler.OrderPaymentCompletedEvent> {
    private final OrderRepository orderRepository;
    private final OrderService orderService;
    private final CartService cartService;
    private final AmqpGateway amqpGateway;

    @Transactional
    @Override
    public void consume(OrderPaymentCompletedEvent event) {
        var order = orderRepository.findById(UUID.fromString(event.orderId))
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "No such order with id " + event.getOrderId()));
        order.markOrderPaid();
        order = orderRepository.save(order);

        cartService.removeOrderItemsFromActiveUserCart(order);

        // Publish the event
        var orderInvoice = orderService.getOrderInvoice(UUID.fromString(event.orderId));
        amqpGateway.publish(new OrderPaidEvent(orderInvoice));
    }

    @Data
    @NoArgsConstructor
    public static class OrderPaymentCompletedEvent implements IRabbitFanoutSubscriber {
        private String orderId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderPaidEvent implements IRabbitFanoutPublisher {
        private OrderInvoiceResponse orderInvoice;
    }
}
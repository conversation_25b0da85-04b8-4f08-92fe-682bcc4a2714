package com.scube.calculation.rabbit;

import com.scube.calculation.dto.fee.FeeDto;
import com.scube.calculation.service.FeeService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class GetFeesQueryHandler extends FanoutListenerRpc<GetFeesQueryHandler.GetFeesQuery, GetFeesQueryHandler.GetFeesQueryResponse> {
    private final FeeService feeService;

    public RabbitResult<GetFeesQueryResponse> consume(GetFeesQuery event) {
        return RabbitResult.of(() -> {
            var fees = feeService.getFeesByKey(event.feeKeys());
            return new GetFeesQueryResponse(fees);
        });
    }

    public record GetFeesQuery(List<String> feeKeys) implements IRabbitFanoutSubscriberRpc<GetFeesQueryResponse> {
    }

    public record GetFeesQueryResponse(List<FeeDto> fees) {
    }
}
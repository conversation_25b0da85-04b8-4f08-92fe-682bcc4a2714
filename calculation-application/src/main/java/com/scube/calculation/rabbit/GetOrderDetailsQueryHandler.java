package com.scube.calculation.rabbit;

import com.scube.calculation.dto.order.OrderInvoiceResponse;
import com.scube.calculation.service.OrderService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class GetOrderDetailsQueryHandler extends FanoutListenerRpc<GetOrderDetailsQueryHandler.GetOrderDetailsQuery, OrderInvoiceResponse> {
    private final OrderService orderService;

    public RabbitResult<OrderInvoiceResponse> consume(GetOrderDetailsQuery event) {
        return RabbitResult.of(() ->
                orderService.getOrderInvoice(UUID.fromString(event.getOrderId()))
        );
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class GetOrderDetailsQuery implements IRabbitFanoutSubscriberRpc<OrderInvoiceResponse> {
        private String orderId;
    }
}
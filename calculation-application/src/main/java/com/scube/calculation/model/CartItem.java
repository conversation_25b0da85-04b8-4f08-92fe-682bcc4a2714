package com.scube.calculation.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.calculation.dto.AddItemRequest;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = CartItem.TABLE_NAME)
@NoArgsConstructor
@Audited
public class CartItem extends AuditableEntity implements Item {
    public static final String TABLE_NAME = "cart_item";
    public static final String CART_ITEM_ID = "cart_item_id";

    @Size(max = 255)
    private String itemTypeId;

    private UUID uniqueItemId;

    @ManyToOne
    @JoinColumn(name = Cart.CART_ID)
    private Cart cart;

    private BigDecimal price = BigDecimal.ZERO;

    @Size(max = 255)
    private String name;

    @Size(max = 255)
    private String description;

    @OneToMany(mappedBy = "item", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CartItemFee> cartItemFees = new ArrayList<>();

    public void setFees(List<Fee> fees) {
        cartItemFees.clear();
        fees.forEach(fee -> {
            CartItemFee cartItemFee = new CartItemFee(this, fee);
            cartItemFees.add(cartItemFee);
        });
    }

    public CartItem(OrderItem orderItem) {
        if (orderItem != null) {
            this.price = orderItem.getPrice();
            this.name = orderItem.getName();
            this.description = orderItem.getDescription();
            this.itemTypeId = orderItem.getItemTypeId();
            this.uniqueItemId = orderItem.getUniqueItemId();
            if (orderItem.getOrderItemFees() != null) {
                this.setCartItemFees(fromOrderItem(orderItem));
            }
            if (orderItem.getProperties() != null) {
                super.setProperties(new HashMap<>(orderItem.getProperties()));
            }
        }
    }

    public List<CartItemFee> fromOrderItem(OrderItem orderItem) {
        return orderItem.getOrderItemFees().stream()
                .map(x -> new CartItemFee(this, x.getFee(), x.calculatePrice()))
                .toList();
    }

    public CartItem(AddItemRequest request) {
        if (request.getBasePrice() == null) request.setBasePrice(BigDecimal.ZERO);
        this.price = request.getBasePrice();
        this.name = request.getName();
        this.description = request.getDescription();
        this.itemTypeId = request.getItemType();
        this.uniqueItemId = request.getItemId();
        if (request.getProperties() != null) {
            super.setProperties(new HashMap<>(request.getProperties()));
        }
    }
}
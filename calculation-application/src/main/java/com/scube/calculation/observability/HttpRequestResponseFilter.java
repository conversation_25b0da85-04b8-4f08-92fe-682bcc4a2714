package com.scube.calculation.observability;

import com.scube.multi.tenant.annotations.NoAsync;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.context.Context;
import jakarta.servlet.AsyncEvent;
import jakarta.servlet.AsyncListener;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

//https://stackoverflow.com/a/70334280/11703800
//https://stackoverflow.com/a/44497698/11703800
@Slf4j
@Component
@Profile("local")
public class HttpRequestResponseFilter extends OncePerRequestFilter {
    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain) throws ServletException, IOException {
        var requestWrapper = new ContentCachingRequestWrapper(request);
        var responseWrapper = new ContentCachingResponseWrapper(response);

        if (ObjectUtils.isEmpty(requestWrapper)) {
            filterChain.doFilter(request, response);
        } else {
            filterChain.doFilter(requestWrapper, responseWrapper);

            // exclude any html, css, js, or image requests
            var uri = requestWrapper.getRequestURI();
            // exclude any html, css, js, or image requests. for example, swagger-ui requests
            if (!ObjectUtils.isEmpty(uri) || !uri.matches(".*\\.(html|css|js|png|jpg|jpeg|gif|svg|ico|woff2|ttf|eot|otf|woff|mp4|webm|ogg|mp3|wav|flac|aac)$")) {
                var tracer = GlobalOpenTelemetry.getTracer("org.springframework.boot");
                Span span = tracer.spanBuilder("Request Response Details")
                        .setParent(Context.current().with(Span.current()))
                        .startSpan();
                span.setAttribute("http.request.path_variables", getPathVariables(requestWrapper));
                span.setAttribute("http.request.params", getRequestParams(requestWrapper));
                span.setAttribute("http.request.body", getRequestBody(requestWrapper));
                span.setAttribute("http.request.method", requestWrapper.getMethod());
                span.setAttribute("http.route", uri);
                span.setAttribute("http.response.status_code", responseWrapper.getStatus());
                getResponseBody(requestWrapper, responseWrapper).thenAccept(body -> {
                    span.setAttribute("http.response.body", body);
                    span.end();
                });
            }
        }
    }

    public String getRequestParams(@NonNull ContentCachingRequestWrapper request) {
        return request.getParameterMap()
                .entrySet().stream()
                .map(e -> e.getKey() + "=" + Arrays.toString(e.getValue()))
                .collect(Collectors.joining(", "));
    }

    public String getPathVariables(@NonNull ContentCachingRequestWrapper request) {
        var pathVariables = request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
        return pathVariables == null ? "" : pathVariables.toString();
    }

    public String getRequestBody(@NonNull ContentCachingRequestWrapper request) {
        return new String(request.getContentAsByteArray(), StandardCharsets.UTF_8);
    }

    @NoAsync
    public CompletionStage<String> getResponseBody(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response) throws IOException {
        var future = new CompletableFuture<String>();

        // for any reactive/async requests
        if (request.isAsyncStarted()) {
            request.getAsyncContext().addListener(new AsyncListener() {
                @NoAsync
                public void onComplete(AsyncEvent asyncEvent) throws IOException {
                    var body = new String(response.getContentAsByteArray());
                    response.copyBodyToResponse(); // IMPORTANT: copy response back into original response
                    future.complete(body);
                }

                public void onTimeout(AsyncEvent asyncEvent) {
                    //ignore
                }

                public void onError(AsyncEvent asyncEvent) {
                    //ignore
                }

                public void onStartAsync(AsyncEvent asyncEvent) {
                    //ignore
                }
            });
        } else {
            var body = new String(response.getContentAsByteArray());
            response.copyBodyToResponse(); // IMPORTANT: copy response back into original response
            future.complete(body);
        }
        return future;
    }
}
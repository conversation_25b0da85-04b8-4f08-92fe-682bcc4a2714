package com.scube.calculation.enums;

import lombok.Getter;

@Getter
public enum CartStatus {
    CART_ACTIVE("cartActive"),
    CART_SUSPENDED("cartSuspended");

    private final String key;

    CartStatus(String key) {
        this.key = key;
    }

    public static CartStatus valueOfIgnoreCase(String key) {
        for (CartStatus status : CartStatus.values()) {
            if (status.getKey().equalsIgnoreCase(key)) {
                return status;
            }
        }
        return null;
    }
}
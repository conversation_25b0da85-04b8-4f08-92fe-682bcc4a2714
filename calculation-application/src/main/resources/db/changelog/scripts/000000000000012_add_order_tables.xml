<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="Ben (generated)" id="1709581918421-1">
        <createTable tableName="order_item">
            <column autoIncrement="true" name="order_item_id" startWith="22814" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="order_item_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="item_type_id" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="price" type="numeric(38, 2)"/>
            <column name="unique_item_id" type="UUID"/>
            <column name="order_id" type="UUID"/>
            <column name="properties" type="JSONB"/>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-2">
        <createTable tableName="order_">
            <column name="order_id" type="UUID">
                <constraints nullable="false" primaryKey="true" primaryKeyName="order__pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="order_number" type="VARCHAR(255)"/>
            <column name="order_paid_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="user_id" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-3">
        <createTable tableName="audit_log_order_">
            <column name="order_id" type="UUID">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_order__pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_order__pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="order_number" type="VARCHAR(255)"/>
            <column name="order_paid_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="user_id" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-5">
        <createTable tableName="audit_log_order_item">
            <column name="order_item_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_order_item_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_order_item_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="item_type_id" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="price" type="numeric(38, 2)"/>
            <column name="unique_item_id" type="UUID"/>
            <column name="order_id" type="UUID"/>
            <column name="properties" type="JSONB"/>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-6">
        <createTable tableName="audit_log_order_item_fees">
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="order_item_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="fee_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-8">
        <createTable tableName="order_item_fees">
            <column name="order_item_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="fee_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-9">
        <addColumn tableName="audit_log_cart_item">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-10">
        <addColumn tableName="cart_item">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-11">
        <addPrimaryKey columnNames="order_item_id, fee_id, revision_id" constraintName="audit_log_order_item_fees_pkey" tableName="audit_log_order_item_fees"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-13">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_order_item" constraintName="fk3mhguxhql6huh8eixng0jlyrl" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-14">
        <addForeignKeyConstraint baseColumnNames="order_item_id" baseTableName="order_item_fees" constraintName="fk8ch4hgisiwqyxxns6hc53bwox" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="order_item_id" referencedTableName="order_item" validate="true"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-15">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_scheduler" constraintName="fkggi0jubkjahhqj63qb2hyn8kb" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-16">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_order_item_fees" constraintName="fkgxq1w94b6eg01d42yord8fpdl" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-17">
        <addForeignKeyConstraint baseColumnNames="order_id" baseTableName="order_item" constraintName="fkjpk230vxy41y3ncj7kpuu9y2e" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="order_id" referencedTableName="order_" validate="true"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-18">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_order_" constraintName="fkkjfwhfk63aipe9midy0gsifrd" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-19">
        <addForeignKeyConstraint baseColumnNames="fee_id" baseTableName="order_item_fees" constraintName="fkpjcoud72ocvkmg65uugirqwg7" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="fee_id" referencedTableName="fee" validate="true"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-25">
        <sql>
            -- Populate customer_order table
            INSERT INTO calculation.order_ (
            order_id, conversion_reference, created_by, created_date, last_modified_by, last_modified_date, order_number, order_paid_date, status, user_id)
            SELECT
            cart_id,
            conversion_reference,
            created_by,
            created_date,
            last_modified_by,
            last_modified_date,
            order_number,
            order_paid_date,
            status,
            user_id
            FROM
            calculation.cart
            WHERE
            status LIKE 'ORDER%';

            -- Populate audit_log_customer_order table
            INSERT INTO calculation.audit_log_order_ (
            order_id, revision_id, revision_type, conversion_reference, last_modified_by, last_modified_date, order_number, order_paid_date, status, user_id)
            SELECT
            cart_id,
            revision_id,
            revision_type,
            conversion_reference,
            last_modified_by,
            last_modified_date,
            order_number,
            order_paid_date,
            status,
            user_id
            FROM
            calculation.audit_log_cart
            WHERE
            status LIKE 'ORDER%';

            -- Populate _order_item table
            INSERT INTO calculation.order_item (
            order_item_id,
            conversion_reference,
            created_by,
            created_date,
            last_modified_by,
            last_modified_date,
            description,
            item_type_id,
            name,
            price,
            unique_item_id,
            order_id
            )
            SELECT
            ci.cart_item_id,
            ci.conversion_reference,
            ci.created_by,
            ci.created_date,
            ci.last_modified_by,
            ci.last_modified_date,
            ci.description,
            ci.item_type_id,
            ci.name,
            ci.price,
            ci.unique_item_id,
            ci.cart_id
            FROM
            calculation.cart_item ci
            WHERE
            ci.cart_id in
            (select cart_id from calculation.cart
            where status LIKE 'ORDER%');

            -- Populate audit_log_order_item table
            INSERT INTO calculation.audit_log_order_item (
            order_item_id,
            conversion_reference,
            last_modified_by,
            last_modified_date,
            description,
            item_type_id,
            name,
            price,
            unique_item_id,
            order_id,
            revision_id,
            revision_type
            )
            SELECT
            ci.cart_item_id,
            ci.conversion_reference,
            ci.last_modified_by,
            ci.last_modified_date,
            ci.description,
            ci.item_type_id,
            ci.name,
            ci.price,
            ci.unique_item_id,
            ci.cart_id,
            ci.revision_id,
            ci.revision_type
            FROM
            calculation.audit_log_cart_item ci
            WHERE
            ci.cart_id in
            (select cart_id from calculation.cart
            where status LIKE 'ORDER%');

            -- Populate order_item_fees table
            INSERT INTO calculation.order_item_fees (
            order_item_id,
            fee_id
            )
            SELECT
            cif.cart_item_id,
            cif.fee_id
            FROM
            calculation.cart_item_fees cif
            WHERE
            cif.cart_item_id in
            (select ci.cart_item_id from calculation.cart_item ci where ci.cart_id in
            (select cart_id from calculation.cart
            where status LIKE 'ORDER%'));

            -- Populate audit_log_order_item_fees table
            INSERT INTO calculation.audit_log_order_item_fees (
            revision_id,
            order_item_id,
            fee_id,
            revision_type
            )
            SELECT
            cif.revision_id,
            cif.cart_item_id,
            cif.fee_id,
            cif.revision_type
            FROM
            calculation.audit_log_cart_item_fees cif
            WHERE
            cif.cart_item_id in
            (select ci.cart_item_id from calculation.cart_item ci where ci.cart_id in
            (select cart_id from calculation.cart
            where status LIKE 'ORDER%'));

            DROP TRIGGER order_number_trigger on calculation.cart;

            ALTER TABLE calculation.cart
            DROP COLUMN order_number,
            DROP COLUMN order_paid_date;

            ALTER TABLE calculation.audit_log_cart
            DROP COLUMN order_number,
            DROP COLUMN order_paid_date;

            CREATE TRIGGER order_number_trigger
            BEFORE INSERT OR UPDATE ON calculation.order_
            FOR EACH ROW
            EXECUTE PROCEDURE calculation.update_order_number();

            DELETE FROM calculation.cart_item_fees where cart_item_id in
            (select cart_item_id from calculation.cart_item where cart_id in
            (select cart_id from calculation.cart where status like 'ORDER%'));
            DELETE FROM calculation.cart_item where cart_id in
            (select cart_id from calculation.cart where status like 'ORDER%');
            DELETE FROM calculation.cart where status like 'ORDER%';
        </sql>
    </changeSet>
    <changeSet author="Ben (generated)" id="1709581918421-26">
        <sql>
            SELECT setval('calculation.order_item_order_item_id_seq',
            (SELECT MAX(order_item_id) FROM calculation.order_item), true);
        </sql>
    </changeSet>
</databaseChangeLog>

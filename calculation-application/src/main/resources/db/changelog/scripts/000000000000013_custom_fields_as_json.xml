<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="postgres (generated)" id="1711482011419-50">
        <addColumn tableName="audit_log_cart">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-51">
        <addColumn tableName="audit_log_fee">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-52">
        <addColumn tableName="audit_log_order_">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-53">
        <addColumn tableName="cart">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-54">
        <addColumn tableName="fee">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-55">
        <addColumn tableName="order_">
            <column name="properties" type="jsonb"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-56">
        <addColumn tableName="audit_log_cart_item">
            <column defaultValueComputed="gen_random_uuid()" name="cart_item_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-57">
        <addColumn tableName="cart_item">
            <column defaultValueComputed="gen_random_uuid()" name="cart_item_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-58">
        <addColumn tableName="audit_log_fee">
            <column defaultValueComputed="gen_random_uuid()" name="fee_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-59">
        <addColumn tableName="fee">
            <column defaultValueComputed="gen_random_uuid()" name="fee_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-60">
        <addColumn tableName="audit_log_order_item">
            <column defaultValueComputed="gen_random_uuid()" name="order_item_uuid" type="uuid"/>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-61">
        <addColumn tableName="order_item">
            <column defaultValueComputed="gen_random_uuid()" name="order_item_uuid" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-62">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_property_type" constraintName="fkph3vvdjiaedcco923ivk6qkxc" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-63">
        <dropUniqueConstraint constraintName="audit_log_property_type_property_type_uuid_key" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-64">
        <dropColumn columnName="created_by" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-65">
        <dropColumn columnName="created_date" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-1">
        <dropForeignKeyConstraint baseTableName="cart_item" constraintName="fk1uobyhgl1wvgt1jpccia8xxs3"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-2">
        <addForeignKeyConstraint baseColumnNames="cart_id" baseTableName="cart_item" constraintName="fk1uobyhgl1wvgt1jpccia8xxs3" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="cart_id" referencedTableName="cart"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-3">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="cart"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-4">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="cart_item"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-5">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-6">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="order_"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-7">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="order_item"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-8">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-9">
        <modifyDataType columnName="created_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-10">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-11">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-12">
        <addNotNullConstraint columnDataType="timestamp" columnName="created_date" tableName="scheduler" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-13">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-14">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="last_modified_by" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-15">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-16">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="cart"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-17">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="cart_item"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-18">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-19">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="order_"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-20">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="order_item"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-21">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-22">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(255)" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-23">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-24">
        <dropNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-25">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="audit_log_scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-26">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-27">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-28">
        <addNotNullConstraint columnDataType="timestamp" columnName="last_modified_date" tableName="scheduler" validate="true"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-29">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-30">
        <dropNotNullConstraint columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-31">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-32">
        <modifyDataType columnName="property_name" newDataType="varchar(250)" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-33">
        <dropDefaultValue columnDataType="varchar(250)" columnName="property_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-34">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-35">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-36">
        <dropDefaultValue columnDataType="varchar(255)" columnName="property_type" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-37">
        <dropNotNullConstraint columnDataType="uuid" columnName="property_type_uuid" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-38">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-39">
        <addDefaultValue columnDataType="uuid" columnName="property_type_uuid" defaultValueComputed="gen_random_uuid()" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-40">
        <modifyDataType columnName="revision_id" newDataType="int" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-41">
        <dropNotNullConstraint columnDataType="smallint(5)" columnName="revision_type" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-42">
        <dropNotNullConstraint columnDataType="uuid" columnName="scheduler_uuid" tableName="scheduler"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-43">
        <dropNotNullConstraint columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-44">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="audit_log_property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-45">
        <dropDefaultValue columnDataType="varchar(100)" columnName="table_name" tableName="property_type"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-46">
        <addUniqueConstraint columnNames="cart_item_uuid" constraintName="uk_ec6tkf8sev2ovqaaly5eu45x0" tableName="cart_item"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-47">
        <addUniqueConstraint columnNames="fee_uuid" constraintName="uk_h3yeochjjp6nltq7roucqrpmx" tableName="fee"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-48">
        <addUniqueConstraint columnNames="order_item_uuid" constraintName="uk_eoufgo0sya55cgvqinha1ba21" tableName="order_item"/>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711482011419-49">
        <addUniqueConstraint columnNames="table_name, property_name" constraintName="property_type_unique" tableName="property_type"/>
    </changeSet>
</databaseChangeLog>

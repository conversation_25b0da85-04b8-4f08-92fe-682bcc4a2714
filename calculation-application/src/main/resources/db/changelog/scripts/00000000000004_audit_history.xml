<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1695361708139-21">
        <createTable tableName="audit_log_cart">
            <column name="cart_id" type="UUID">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_cart_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_cart_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="user_id" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-22">
        <createTable tableName="audit_log_fee">
            <column name="fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_fee_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_fee_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="amount" type="numeric(38, 2)"/>
            <column name="collapsible" type="BOOLEAN"/>
            <column name="fee_name" type="VARCHAR(255)"/>
            <column name="key" type="VARCHAR(255)"/>
            <column name="operation" type="VARCHAR(255)"/>
            <column name="payable_id" type="INTEGER"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-23">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807" minValue="1" sequenceName="audit_log_revision_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-24">
        <createTable tableName="audit_log_cart_item">
            <column name="cart_item_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_cart_item_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_cart_item_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="item_type_id" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="price" type="numeric(38, 2)"/>
            <column name="unique_item_id" type="UUID"/>
            <column name="cart_id" type="UUID"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-25">
        <createTable tableName="audit_log_cart_item_fees">
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="cart_item_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="fee_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-26">
        <createTable tableName="audit_log_revision">
            <column name="audit_log_revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_revision_pkey"/>
            </column>
            <column name="timestamp" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="auditor" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-27">
        <addColumn tableName="cart">
            <column name="conversion_reference" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-28">
        <addColumn tableName="cart_item">
            <column name="conversion_reference" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-29">
        <addColumn tableName="fee">
            <column name="conversion_reference" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-30">
        <addPrimaryKey columnNames="cart_item_id, fee_id, revision_id" constraintName="audit_log_cart_item_fees_pkey" tableName="audit_log_cart_item_fees"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-31">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_fee" constraintName="fk675w8hwwxi6o304pnym089iph" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-32">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_cart_item" constraintName="fkcngiokc126acvrya4mtdipe00" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-33">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_cart" constraintName="fkll6o5y61qc19mydvqa6hi9jrm" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-34">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_cart_item_fees" constraintName="fkr86hnd70xh13q16m65f56a341" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-1">
        <dropForeignKeyConstraint baseTableName="cart_item" constraintName="fk1uobyhgl1wvgt1jpccia8xxs3"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-2">
        <addForeignKeyConstraint baseColumnNames="cart_id" baseTableName="cart_item" constraintName="fk1uobyhgl1wvgt1jpccia8xxs3" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="cart_id" referencedTableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-3">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-4">
        <dropDefaultValue columnDataType="varchar(1000)" columnName="created_by" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-5">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-6">
        <dropDefaultValue columnDataType="varchar(1000)" columnName="created_by" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-7">
        <modifyDataType columnName="created_by" newDataType="varchar(1000)" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-8">
        <dropDefaultValue columnDataType="varchar(1000)" columnName="created_by" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-9">
        <dropDefaultValue columnDataType="timestamp" columnName="created_date" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-10">
        <dropDefaultValue columnDataType="timestamp" columnName="created_date" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-11">
        <dropDefaultValue columnDataType="timestamp" columnName="created_date" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-12">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-13">
        <dropDefaultValue columnDataType="varchar(1000)" columnName="last_modified_by" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-14">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-15">
        <dropDefaultValue columnDataType="varchar(1000)" columnName="last_modified_by" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-16">
        <modifyDataType columnName="last_modified_by" newDataType="varchar(1000)" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-17">
        <dropDefaultValue columnDataType="varchar(1000)" columnName="last_modified_by" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-18">
        <dropDefaultValue columnDataType="timestamp" columnName="last_modified_date" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-19">
        <dropDefaultValue columnDataType="timestamp" columnName="last_modified_date" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695361708139-20">
        <dropDefaultValue columnDataType="timestamp" columnName="last_modified_date" tableName="fee"/>
    </changeSet>
</databaseChangeLog>

package com.scube.config.rule_engine.web;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.config.permission.Permissions;
import com.scube.config_utils.rule_engine.IRuleEngine;
import com.scube.config_utils.rule_engine.IRuleEngineEvaluateMultipleResponse;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

@Slf4j
@RestController
@RequestMapping("/me/rule")
@Validated
@GenerateHttpExchange(ServiceUrlConstant.CONFIG_SERVICE)
@RequiredArgsConstructor
public class LoggedInUserRuleEngineController {
    private final IRuleEngine ruleEngine;

    @PostMapping("/evaluate")
    @RolesAllowed(Permissions.LoggedInUserRuleEngine.EVALUATE)
    public IRuleEngineEvaluateMultipleResponse evaluate(@RequestParam Collection<String> rules, @RequestBody JsonNode args) {
        return ruleEngine.evaluate(rules, args);
    }
}
package com.scube.config.json_storage;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.audit.auditable.services.AuditableEntityService;
import com.scube.audit.exception.EntityNotFoundException;
import com.scube.config.json_storage.db.JsonStorage;
import com.scube.config.json_storage.db.JsonStorageRepository;
import com.scube.config.json_storage.web.JsonStorageHistoryResponse;
import com.scube.lib.misc.JsonUtils;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.hibernate.envers.AuditReader;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;

@Service
@Transactional
@RequiredArgsConstructor
public class JsonStorageService extends AuditableEntityService<Long, JsonStorage, JsonStorageRepository> {
    private final AuditReader auditReader;

    public void createUpdate(Map<String, Object> properties, JsonNode jsonStorage) {
        var jsonStorageOptional = this.repository.findExactByProperties(properties);
        if (jsonStorageOptional.isPresent()) {
            var jsonStorageEntity = jsonStorageOptional.get();
            jsonStorageEntity.setJsonData(jsonStorage);
            this.repository.save(jsonStorageEntity);
        } else {
            this.repository.save(new JsonStorage(properties, jsonStorage));
        }
    }

    public <T> T getAndSerialize(Map<String, Object> properties, Class<T> clazz) {
        var resultOptional = this.repository.findExactByProperties(properties);
        if (resultOptional.isEmpty())
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "JsonStorage with properties " + properties + " not found");
        return JsonUtils.deserializeJson(resultOptional.get().getJsonData(), clazz);
    }

    public ResponseEntity<JsonNode> get(Long id, UUID uuid, Map<String, Object> fields) {
        try {
            var jsonData = getExactOrThrow(id, uuid, fields).getJsonData();
            return ResponseEntity.ok(jsonData);
        } catch (ResponseStatusException | EntityNotFoundException e) {
            return ResponseEntity.noContent().build();
        }
    }

    public ResponseEntity<JsonStorageHistoryResponse> getHistory(Long id, UUID uuid, Map<String, Object> fields) {
        JsonStorage jsonStorage;
        try {
            jsonStorage = getExactOrThrow(id, uuid, fields);
        } catch (ResponseStatusException | EntityNotFoundException e) {
            return ResponseEntity.noContent().build();
        }
        var result = new ArrayList<JsonStorageHistoryResponse.JsonStorageHistory>();
        auditReader.getRevisions(JsonStorage.class, jsonStorage.getId())
                .stream().sorted((a, b) -> b.intValue() - a.intValue())
                .forEach(revision -> {
                    var auditJsonStorage = auditReader.find(JsonStorage.class, jsonStorage.getId(), revision);
                    result.add(new JsonStorageHistoryResponse.JsonStorageHistory(
                            revision, auditJsonStorage.getJsonData()
                    ));
                });
        return ResponseEntity.ok(new JsonStorageHistoryResponse(result));
    }
}
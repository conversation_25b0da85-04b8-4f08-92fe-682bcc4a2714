package com.scube.config;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.config.app_property.rabbit.AppPropertyChangedEvent;
import com.scube.config.json_storage.rabbit.JsonStorageChangedEvent;
import com.scube.config.permission.Permissions;
import com.scube.config.sql_storage.rabbit.SqlStorageChangedEvent;
import com.scube.config_utils.caching.EvictAllCacheEvent;
import com.scube.rabbit.core.AmqpGateway;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/cache")
@Validated
@GenerateHttpExchange(ServiceUrlConstant.CONFIG_SERVICE)
@RequiredArgsConstructor
public class CacheController {
    private final AmqpGateway amqpGateway;

    @PostMapping("/evict/all")
    @RolesAllowed(Permissions.Cache.EVICT_CACHE)
    public void evictCache() {
        amqpGateway.publish(new EvictAllCacheEvent());
    }

    @PostMapping("/evict/app-property")
    @RolesAllowed(Permissions.Cache.EVICT_APP_PROPERTY_CACHE)
    public void evictAppPropertyCache() {
        log.info("Evicting cache for app property");
        amqpGateway.publish(new AppPropertyChangedEvent());
    }

    @PostMapping("/evict/json-storage")
    @RolesAllowed(Permissions.Cache.EVICT_JSON_STORAGE_CACHE)
    public void evictJsonStorageCache() {
        log.info("Evicting cache for json storage");
        amqpGateway.publish(new JsonStorageChangedEvent());
    }

    @PostMapping("/evict/sql-storage")
    @RolesAllowed(Permissions.Cache.EVICT_SQL_STORAGE_CACHE)
    public void evictSqlStorageCache() {
        log.info("Evicting cache for sql storage");
        amqpGateway.publish(new SqlStorageChangedEvent());
    }
}
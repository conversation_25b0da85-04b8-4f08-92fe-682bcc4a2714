package com.scube.config.app_property;

import com.scube.audit.auditable.services.AuditableEntityService;
import com.scube.config.app_property.db.AppProperty;
import com.scube.config.app_property.db.AppPropertyRepository;
import com.scube.lib.misc.encryption.EncryptionService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Validated
@Transactional
public class AppPropertyService extends AuditableEntityService<Long, AppProperty, AppPropertyRepository> {
    public static final String CIPHER_PREFIX = "{cipher}";
    public static final String ENCRYPT_PREFIX = "{encrypt}";
    private final EncryptionService encryptionService;

    public Map<String, String> getProperties(boolean redactSensitive) {
        log.debug("Getting all properties");
        if (!redactSensitive) return getProperties();
        return repository.findAll(Sort.by(Sort.Direction.ASC, "key")).stream()
                .collect(
                        LinkedHashMap::new,
                        (map, property) -> {
                            boolean isSensitive = isSensitiveProperty(property.getKey(), property.getValue()) || isCipherProperty(property.getValue());
                            map.put(property.getKey(), isSensitive ? "**redacted**" : encryptDecryptProperty(property.getValue()));
                        },
                        LinkedHashMap::putAll
                );
    }

    public Map<String, String> getProperties() {
        log.debug("Getting all properties");
        return repository.findAll(Sort.by(Sort.Direction.ASC, "key")).stream()
                .collect(LinkedHashMap::new, (map, property) -> map.put(property.getKey(), encryptDecryptProperty(property.getValue())), LinkedHashMap::putAll);
    }

    public String getProperty(@NonNull String key, boolean redactSensitive) {
        log.debug("Getting property with key: {}", key);
        if (!redactSensitive) return getProperty(key);
        return repository.findByKey(key)
                .map(x -> {
                    boolean isSensitive = isSensitiveProperty(x.getKey(), x.getValue()) || isCipherProperty(x.getValue());
                    return isSensitive ? "**redacted**" : encryptDecryptProperty(x.getValue());
                })
                .orElse(null);
    }

    public String getProperty(@NonNull String key) {
        log.debug("Getting property with key: {}", key);
        return repository.findByKey(key)
                .map(x -> encryptDecryptProperty(x.getValue()))
                .orElse(null);
    }

    public void deleteProperty(@NonNull String key) {
        log.debug("Deleting property with key: {}", key);
        Optional<AppProperty> appProperty = repository.findByKey(key);
        appProperty.ifPresent(repository::delete);
    }

    public void setProperties(Map<String, Object> properties) {
        log.debug("Setting properties: {}", properties);
        properties.forEach(this::setProperty);
    }

    private void setProperty(@NonNull String key, @Nullable Object value) {
        log.debug("Setting property with key: {} to value: {}", key, value);
        if (isSensitiveProperty(key, value) && !isEncryptedProperty(value)) {
            value = ENCRYPT_PREFIX + value;
        }
        var valueStr = encryptDecryptProperty(Optional.ofNullable(value).orElse("").toString()); // encrypt if needed
        AppProperty appProperty = repository.findByKey(key).orElse(new AppProperty(key, valueStr));
        appProperty.setValue(valueStr);
        repository.save(appProperty);
    }

    private boolean isEncryptedProperty(@Nullable Object value) {
        if (ObjectUtils.isEmpty(value)) return false;
        return value.toString().startsWith(ENCRYPT_PREFIX);
    }

    public boolean isSensitiveProperty(String key, Object value) {
        List<String> autoEncryptList = List.of("password", "secret", "username", "clientId");
        return !ObjectUtils.isEmpty(value) && autoEncryptList.stream().anyMatch(x -> key.toLowerCase().contains(x));
    }

    public boolean isCipherProperty(String value) {
        return !ObjectUtils.isEmpty(value) && value.startsWith(CIPHER_PREFIX);
    }

    private String encryptDecryptProperty(@Nullable String value) {
        if (ObjectUtils.isEmpty(value)) return value;
        if (value.startsWith(CIPHER_PREFIX)) {
            return encryptionService.decrypt(value.substring(CIPHER_PREFIX.length()));
        } else if (value.startsWith(ENCRYPT_PREFIX)) {
            return CIPHER_PREFIX + encryptionService.encrypt(value.substring(ENCRYPT_PREFIX.length()));
        }
        return value;
    }
}
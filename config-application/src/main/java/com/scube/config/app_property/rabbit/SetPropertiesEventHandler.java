package com.scube.config.app_property.rabbit;

import com.scube.config.app_property.AppPropertyService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class SetPropertiesEventHandler extends FanoutListener<SetPropertiesEventHandler.SetPropertiesEvent> {
    private final AppPropertyService appPropertyService;

    @Override
    public void consume(SetPropertiesEvent event) {
        appPropertyService.setProperties(event.properties());
    }

    public record SetPropertiesEvent(
            Map<String, Object> properties) implements IRabbitFanoutSubscriber {
    }
}
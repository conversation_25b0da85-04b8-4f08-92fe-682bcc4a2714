package com.scube.config.sql_storage.rabbit.create_update;

import com.scube.config.sql_storage.SqlStorageMapper;
import com.scube.config.sql_storage.SqlStorageService;
import com.scube.config.sql_storage.web.dto.SqlStorageRequest;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class CreateUpdateSqlStorageCommandHandler extends FanoutListener<CreateUpdateSqlStorageCommand> {
    private final SqlStorageService jsonStorageService;
    private final SqlStorageMapper jsonStorageMapper;

    @Override
    public void consume(CreateUpdateSqlStorageCommand event) {
        SqlStorageRequest request = jsonStorageMapper.toRequest(event);
        jsonStorageService.createOrUpdate(request);
    }
}
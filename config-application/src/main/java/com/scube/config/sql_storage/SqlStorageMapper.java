package com.scube.config.sql_storage;

import com.scube.config.sql_storage.db.SqlStorage;
import com.scube.config.sql_storage.rabbit.create_update.CreateUpdateSqlStorageCommand;
import com.scube.config.sql_storage.web.dto.SqlStorageRequest;
import com.scube.config.sql_storage.web.dto.SqlStorageResponse;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

import java.util.Collection;

@Mapper(componentModel = "spring")
public abstract class SqlStorageMapper {

    public abstract SqlStorageResponse toDto(SqlStorage sqlStorage);

    public abstract Collection<SqlStorageResponse> toDto(Collection<SqlStorage> sqlStorages);

    public abstract void updateEntity(@MappingTarget SqlStorage entity, SqlStorageRequest request);

    public abstract SqlStorageRequest toRequest(CreateUpdateSqlStorageCommand event);
}
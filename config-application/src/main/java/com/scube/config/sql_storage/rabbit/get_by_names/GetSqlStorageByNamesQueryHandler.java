package com.scube.config.sql_storage.rabbit.get_by_names;

import com.scube.config.sql_storage.SqlStorageService;
import com.scube.config.sql_storage.web.dto.SqlStorageResponse;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
@RequiredArgsConstructor
class GetSqlStorageByNamesQueryHandler extends FanoutListenerRpc<GetSqlStorageByNamesQuery, GetSqlStorageByNamesQueryResponse> {
    private final SqlStorageService sqlStorageService;

    @Override
    public RabbitResult<GetSqlStorageByNamesQueryResponse> consume(GetSqlStorageByNamesQuery event) {
        return RabbitResult.of(() -> {
            Collection<SqlStorageResponse> result = sqlStorageService.getAllByNames(event.names());
            return new GetSqlStorageByNamesQueryResponse(result);
        });
    }
}
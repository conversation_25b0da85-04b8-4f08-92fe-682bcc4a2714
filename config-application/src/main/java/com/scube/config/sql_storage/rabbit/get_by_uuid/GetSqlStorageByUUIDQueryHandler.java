package com.scube.config.sql_storage.rabbit.get_by_uuid;

import com.scube.config.sql_storage.SqlStorageMapper;
import com.scube.config.sql_storage.SqlStorageService;
import com.scube.config.sql_storage.web.dto.SqlStorageResponse;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class GetSqlStorageByUUIDQueryHandler extends FanoutListenerRpc<GetSqlStorageByUUIDQuery, SqlStorageResponse> {
    private final SqlStorageService sqlStorageService;
    private final SqlStorageMapper mapper;

    @Override
    public RabbitResult<SqlStorageResponse> consume(GetSqlStorageByUUIDQuery event) {
        return RabbitResult.of(() -> sqlStorageService.getByUuidOrThrow(event.uuid()));
    }
}
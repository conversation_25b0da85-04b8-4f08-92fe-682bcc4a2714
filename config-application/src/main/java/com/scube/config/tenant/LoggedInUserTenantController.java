package com.scube.config.tenant;

import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import com.scube.multi.tenant.annotations.SkipTenantValidation;
import com.scube.multi.tenant.tenancy.keycloak.ITenant;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

@RestController
@RequestMapping("/me/tenant")
@RequiredArgsConstructor
@Validated
@GenerateHttpExchange(ServiceUrlConstant.CONFIG_SERVICE)
public class LoggedInUserTenantController {
    private final TenantService tenantService;

    @PostMapping("/{tenantIdentifier}/user/join")
    @ResponseStatus(HttpStatus.OK)
    @SkipTenantValidation
    public void joinTenant(@PathVariable @Size(max = 50) String tenantIdentifier, @AuthenticationPrincipal MyOpenIdClaimSet claimSet) {
        var userId = claimSet.getSubject();
        tenantService.join(tenantIdentifier, userId);
    }

    @PostMapping("/{tenantIdentifier}/user/leave")
    @ResponseStatus(HttpStatus.OK)
    @SkipTenantValidation
    public void leaveTenant(@PathVariable @Size(max = 50) String tenantIdentifier, @AuthenticationPrincipal MyOpenIdClaimSet claimSet) {
        var userId = claimSet.getSubject();
        tenantService.leave(tenantIdentifier, userId);
    }

    @PostMapping("/{tenantIdentifier}/user/make-active")
    @ResponseStatus(HttpStatus.OK)
    @SkipTenantValidation
    public void makeActiveTenant(@PathVariable @Size(max = 50) String tenantIdentifier, @AuthenticationPrincipal MyOpenIdClaimSet claimSet) {
        var userId = claimSet.getSubject();
        tenantService.makeActiveTenant(tenantIdentifier, userId);
    }

    @GetMapping("/user/active")
    @ResponseStatus(HttpStatus.OK)
    @SkipTenantValidation
    public ITenant getActiveTenant(@AuthenticationPrincipal MyOpenIdClaimSet claimSet) {
        var userId = claimSet.getSubject();
        return tenantService.getActiveTenant(userId);
    }

    @PostMapping("/user/clear-active")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo("Clear the active tenant for the user")
    @SkipTenantValidation
    public void removeActiveTenant(@AuthenticationPrincipal MyOpenIdClaimSet claimSet) {
        var userId = claimSet.getSubject();
        var activeTenant = tenantService.getActiveTenant(userId);
        if (ObjectUtils.isEmpty(activeTenant)) return;
        if (ObjectUtils.isEmpty(activeTenant.getName())) return;
        tenantService.leave(activeTenant.getName(), userId);
    }

    @GetMapping("/user/tenants")
    @ResponseStatus(HttpStatus.OK)
    @SkipTenantValidation
    public Set<ITenant> getTenants(@AuthenticationPrincipal MyOpenIdClaimSet claimSet) {
        var userId = claimSet.getSubject();
        return tenantService.getUserTenants(userId);
    }

    @GetMapping("/user/roles")
    @ResponseStatus(HttpStatus.OK)
    @SkipTenantValidation
    public Set<String> getRoles(@AuthenticationPrincipal MyOpenIdClaimSet claimSet) {
        var userId = claimSet.getSubject();
        return tenantService.getUserRoles(userId);
    }
}
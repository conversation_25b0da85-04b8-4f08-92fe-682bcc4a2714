package com.scube.config.tenant;

import com.scube.multi.tenant.management.ITenantManagementService;
import com.scube.multi.tenant.tenancy.keycloak.ITenant;
import com.scube.multi.tenant.tenancy.keycloak.ITenantStorage;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
@RequiredArgsConstructor
public class TenantService {
    private final ITenantManagementService tenantManagementService;
    private final ITenantStorage tenantStorage;

    public void create(@NonNull String tenantIdentifier) {
        tenantManagementService.createNew(tenantIdentifier);
    }

    public void join(@NonNull String tenantIdentifier, @NonNull String userId) {
        tenantStorage.join(tenantIdentifier, userId);
    }

    public void leave(@NonNull String tenantIdentifier, @NonNull String userId) {
        tenantStorage.leave(tenantIdentifier, userId);
    }

    public Set<ITenant> getUserTenants(@NonNull String userId) {
        return tenantStorage.getUserTenants(userId, true);
    }

    public Set<String> getUserRoles(@NonNull String userId) {
        return tenantStorage.getActiveTenant(userId)
                .map(tenant -> tenantStorage.getUserRoles(tenant.getName(), userId))
                .orElse(Set.of());
    }

    public void makeActiveTenant(@NonNull String tenantIdentifier, @NonNull String userId) {
        tenantStorage.makeActiveTenant(tenantIdentifier, userId);
    }

    @Nullable
    public ITenant getActiveTenant(@NonNull String userId) {
        return tenantStorage.getActiveTenant(userId)
                .orElse(null);
    }

    public Set<ITenant> getAllTenants() {
        return tenantStorage.getAllTenants(true);
    }

    public Set<String> getIdentityProviders() {
        return tenantStorage.getIdentityProviders("");
    }

    public void removeActiveTenant(String userId) {
        tenantStorage.removeActiveTenant(userId);
    }
}
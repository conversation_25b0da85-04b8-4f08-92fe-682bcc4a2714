package com.scube.notification.features.scheduling.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Profile("test")
public class NotificationScheduler {
    public void start() {
        log.debug("NotificationScheduler.start()");
    }

    public void stop() {
        log.debug("NotificationScheduler.stop()");
    }

    public void refresh() {
        log.debug("NotificationScheduler.refresh()");
    }
}

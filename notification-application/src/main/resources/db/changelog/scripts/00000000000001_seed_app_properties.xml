<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="ben" id="1690815186088-17">
        <sql>
            INSERT INTO app_properties (name, field_value, created_date, last_modified_date, uuid)
            VALUES
            ('task-schedule-notification-processing-cron', '*/10 * * * * *', current_date, current_date, gen_random_uuid()),
            ('document-storage-url', 'http://scube-document-service-srv.backend.svc.cluster.local:9003/api/document-service', current_date, current_date, gen_random_uuid())
            ON CONFLICT ON CONSTRAINT uk_crbdhq8ruhgfgi1cq0mufvm7v DO NOTHING;
        </sql>
    </changeSet>
</databaseChangeLog>

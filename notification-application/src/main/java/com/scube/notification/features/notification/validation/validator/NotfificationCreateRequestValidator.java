package com.scube.notification.features.notification.validation.validator;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.notification.db.entity.NotificationType;
import com.scube.notification.features.notification.dto.NotificationCreateRequest;
import com.scube.notification.features.processing.email.model.Email;
import com.scube.notification.features.processing.sms.model.SMS;
import com.scube.notification.features.notification.validation.annotation.ValidNotificationCreateRequest;
import com.scube.notification.features.notification.validation.service.ValidationService;
import jakarta.validation.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class NotfificationCreateRequestValidator
        implements ConstraintValidator<ValidNotificationCreateRequest, NotificationCreateRequest> {
    @Autowired
    private ValidationService validationService;

    public void initialize(ValidNotificationCreateRequest constraintAnnotation) {

    }

    @Override
    public boolean isValid(NotificationCreateRequest request,
                           ConstraintValidatorContext context) {
        List<String> errorMessages = new ArrayList<>();

        NotificationType notificationType = request.getNotificationType();
        JsonNode jsonNode = request.getNotification();

        ObjectMapper objectMapper = new ObjectMapper();

        String errorMessage = "";

        if (request.getNotification() != null && notificationType != null) {
            try {
                switch (notificationType) {
                    case EMAIL:
                        Email email = objectMapper.treeToValue(jsonNode, Email.class);
                        var violations = isValid(email);

                        if (!violations.isEmpty()) {
                            errorMessage = violations.stream()
                                    .map(ConstraintViolation::getMessage)
                                    .collect(Collectors.joining(", "));
                        }
                        break;
                    case SMS:
                        SMS sms = objectMapper.treeToValue(jsonNode, SMS.class);
                        var violations2 = isValid(sms);

                        if (!violations2.isEmpty()) {
                            errorMessage = violations2.stream()
                                    .map(ConstraintViolation::getMessage)
                                    .collect(Collectors.joining(", "));
                        }
                        break;
                    default:
                        errorMessage = "Invalid notification type";
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }

        if (isNotBlank(errorMessage)) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(errorMessage)
                    .addPropertyNode("notification")
                    .addConstraintViolation();
            return false;
        }

        return true;
    }

    private Set<ConstraintViolation<Email>> isValid(Email email) {
        return Validation.buildDefaultValidatorFactory().getValidator().validate(email);
    }

    private Set<ConstraintViolation<SMS>> isValid(SMS sms) {
        return Validation.buildDefaultValidatorFactory().getValidator().validate(sms);
    }
}

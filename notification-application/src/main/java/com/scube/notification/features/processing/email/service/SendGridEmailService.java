package com.scube.notification.features.processing.email.service;

import com.scube.notification.exception.BadRequestException;
import com.scube.notification.exception.InternalServerErrorException;
import com.scube.notification.features.processing.email.model.Attachment;
import com.scube.notification.features.processing.email.model.Email;
import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Attachments;
import com.sendgrid.helpers.mail.objects.Content;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Base64;
import java.util.List;

import static com.scube.notification.features.processing.email.constants.ServiceNames.SENDGRID;
import static org.apache.logging.log4j.util.Strings.isNotBlank;

@Service(SENDGRID)
@Slf4j
@AllArgsConstructor
public class SendGridEmailService implements IEmailService {
    @Override
    public void send(Email email) {
        log.debug("SendGridEmailService.send()");

        Mail mail = generateSendGridEmail(email);

        var sendgrid = new SendGrid(System.getenv("SENDGRID_API_KEY"));
        var request = new Request();

        try {
            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());
            var response = sendgrid.api(request);

            if (response.getStatusCode() >= 400) {
                log.error("SendGrid failed: " + response.getBody());
                if (response.getStatusCode() == HttpStatus.BAD_REQUEST.value()) {
                    throw new BadRequestException("SendGrid failed: " + response.getBody());
                } else {
                    throw new InternalServerErrorException("SendGrid failed: " + response.getBody());
                }
            }

            log.debug(String.valueOf(response.getStatusCode()));
            log.debug(response.getBody());
            log.debug(response.getHeaders().toString());
        } catch (IOException e) {
            log.error("SendGridEmailService.send() - IOException: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private Mail generateSendGridEmail(Email email) {
        var from = new com.sendgrid.helpers.mail.objects.Email(email.getFrom());
        var subject = email.getSubject();
        var to = new com.sendgrid.helpers.mail.objects.Email(email.getTo());
        var content = new Content(isNotBlank(email.getContentType()) ? email.getContentType() : "text/plain", email.getBody());
        var mail = new Mail(from, subject, to, content);

        for (Attachments attachment : generateSendGridAttachments(email.getAttachments())) {
            mail.addAttachments(attachment);
        }
        return mail;
    }

    private List<Attachments> generateSendGridAttachments(List<Attachment> attachments) {
        log.debug("SendGridEmailService.getAttachments()");

        return attachments.stream().map(attachment -> {
            var sgAttachment = new Attachments();

            sgAttachment.setContent(Base64.getEncoder().encodeToString(attachment.getContent()));
            sgAttachment.setType(attachment.getContentType());
            sgAttachment.setFilename(attachment.getName());
            return sgAttachment;
        }).toList();
    }
}





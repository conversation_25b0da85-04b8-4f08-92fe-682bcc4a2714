package com.scube.notification.features.notification.service;

import java.util.List;
import java.util.UUID;

import com.scube.notification.db.entity.NotificationStatus;
import com.scube.notification.db.projection.NotificationProjection;
import com.scube.notification.exception.NotFoundException;
import com.scube.notification.features.notification.dto.CancelNotificationRequest;
import com.scube.notification.features.notification.dto.NotificationCreateRequest;
import com.scube.notification.features.status.service.NotificationStatusService;
import com.scube.notification.features.notification.validation.service.ValidationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.scube.notification.db.entity.Notification;
import com.scube.notification.db.repository.NotificationRepository;

import static com.scube.notification.features.status.constants.StatusConstants.CANCELED;
import static com.scube.notification.features.status.constants.StatusConstants.PENDING;


@AllArgsConstructor
@Service
@Slf4j
public class NotificationService {

    private final NotificationRepository notificationRepository;
    private final NotificationStatusService notificationStatusService;
    private final ValidationService validationService;

    public List<NotificationProjection> findAll() {
        log.debug("NotificationService.findAll()");
        return notificationRepository.findAllBy();
    }

    public Notification findByUuid(UUID uuid) {
        log.debug("NotificationService.findByUuid()");
        return notificationRepository.findByUuid(uuid).orElseThrow(() -> new NotFoundException("Notification not found"));
    }

    public NotificationProjection findProjectionByUuid(UUID uuid) {
        log.debug("NotificationService.findProjectionByUuid()");
        return notificationRepository.findProjectionByUuid(uuid).orElseThrow(() -> new NotFoundException("Notification not found"));
    }

    public NotificationProjection create(NotificationCreateRequest request) {
        log.debug("NotificationService.create()");
        Notification notification = Notification.builder()
                .notificationStatus(notificationStatusService.findByName(PENDING))
                .notification(request.getNotification())
                .executionTs(request.getExecutionTs())
                .topic(request.getTopic())
                .tag(request.getTag())
                .correlationId(request.getCorrelationId())
                .notificationType(request.getNotificationType())
                .build();

        return findProjectionByUuid(save(notification).getUuid());
    }

    public Notification save(Notification notification) {
        log.debug("NotificationService.save()");
        return notificationRepository.save(notification);
    }

    public void deleteByUuid(UUID uuid) {
        log.debug("NotificationService.deleteByUuid()");
        notificationRepository.delete(findByUuid(uuid));
    }
    
    public List<Notification> findPendingNotificationsReadyToSend() {
        return notificationRepository.findByStatusName(PENDING);
    }

    /**
     * Retrieve PENDING notifications by uuid, correlationId, tag or topic
     * and set their status to CANCELED.
     *
     * @param request
     */
    public void cancel(CancelNotificationRequest request) {
        log.debug("NotificationService.cancel()");

        List<Notification> notifications = notificationRepository.searchForPendingByFields(
                request.getNotificationUuid(), request.getCorrelationId(), request.getTag(), request.getTopic());

        if (!notifications.isEmpty()) {
            NotificationStatus canceled = notificationStatusService.findByName(CANCELED);

            for (Notification notification : notifications) {
                notification.setNotificationStatus(canceled);
                notificationRepository.save(notification);
            }
        }
    }
}


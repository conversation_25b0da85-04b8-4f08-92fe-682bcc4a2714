package com.scube.notification.features.notification.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.UUID;

@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Data
public abstract class NotificationMetadata {
    private UUID correlationId;
    @NotEmpty(message = "tag must not be empty")
    private String tag;
    @NotEmpty(message = "topic must not be empty")
    private String topic;
    private LocalDateTime executionTs;
    @NotEmpty(message = "createdBy must not be empty")
    private String createdBy;
}

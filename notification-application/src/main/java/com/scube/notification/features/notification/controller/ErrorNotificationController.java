package com.scube.notification.features.notification.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.notification.features.notification.dto.ErrorEmailRequest;
import com.scube.notification.features.permission.Permissions;
import com.scube.notification.features.processing.email.service.SMTPErrorEmailService;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/error")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.NOTIFICATION_SERVICE)
public class ErrorNotificationController {
    private final SMTPErrorEmailService smtpErrorEmailService;

    @PostMapping("/email")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.ErrorNotification.SEND_EMAIL)
    public void sendEmail(@RequestBody @Valid ErrorEmailRequest request) {
        log.debug("ErrorNotificationController.sendEmail()");
        smtpErrorEmailService.send(request);
    }
}

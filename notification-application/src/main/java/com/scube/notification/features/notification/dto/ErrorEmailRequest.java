package com.scube.notification.features.notification.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatusCode;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ErrorEmailRequest {
    private String serviceName;
    private String exception;
    private String message;
    private HttpStatusCode httpStatusCode;
    private String stackTrace;
    private String contextPath;

    public String emailBody() {
        return "Service Name: " + serviceName + "\n\n" +
                "HTTP Status Code: " + httpStatusCode + "\n\n" +
                "Context Path: " + contextPath + "\n\n" +
                "Exception: " + exception + "\n\n" +
                "Message: " + message + "\n\n" +
                "Stack Trace: " + stackTrace;
    }

    public String subject() {
        StringBuilder subject = new StringBuilder();

        if (httpStatusCode.is4xxClientError()) {
            subject.append("Client Error ");
        } else if (httpStatusCode.is5xxServerError()) {
            subject.append("Server Error ");
        }

        subject.append("occurred in " + serviceName+ " - " + httpStatusCode);
        return subject.toString();
    }
}
